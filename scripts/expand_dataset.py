#!/usr/bin/env python3
"""
Data Expansion Script for Enhanced English Language Model Training

This script implements Phase 1 of the data expansion plan, downloading and processing
manageable subsets of high-quality English datasets.
"""

import os
import sys
import json
import random
import argparse
from typing import List, Dict, Any
from pathlib import Path

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

try:
    from datasets import load_dataset
    DATASETS_AVAILABLE = True
except ImportError:
    print("⚠️  Hugging Face datasets not available. Install with: pip install datasets")
    DATASETS_AVAILABLE = False

from src.preprocessing.improved_tokenizer import ImprovedBPETokenizer
from src.utils.logging import SimpleLogger


class DataExpansionPipeline:
    """Pipeline for downloading and processing expanded datasets."""
    
    def __init__(self, output_dir: str = "data/raw/expanded_english", 
                 target_size_mb: int = 1000, logger: SimpleLogger = None):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        self.target_size_mb = target_size_mb
        self.logger = logger or SimpleLogger()
        self.current_size_mb = 0
        
    def estimate_text_size_mb(self, text: str) -> float:
        """Estimate text size in MB."""
        return len(text.encode('utf-8')) / (1024 * 1024)
    
    def quality_filter(self, text: str) -> bool:
        """Apply quality filtering to text."""
        # Length filtering
        if len(text) < 100 or len(text) > 50000:
            return False
            
        # Basic quality checks
        if text.count('\n') / len(text) > 0.1:  # Too many line breaks
            return False
            
        # Check for reasonable word density
        words = text.split()
        if len(words) < 20:  # Too few words
            return False
            
        # Check for excessive repetition
        unique_words = set(words)
        if len(unique_words) / len(words) < 0.3:  # Too repetitive
            return False
            
        return True
    
    def download_openwebtext_subset(self, sample_ratio: float = 0.001) -> List[str]:
        """Download and process OpenWebText subset."""
        if not DATASETS_AVAILABLE:
            self.logger.log("❌ Datasets library not available. Skipping OpenWebText.")
            return []

        self.logger.log(f"📥 Downloading OpenWebText subset (sample ratio: {sample_ratio})")

        try:
            # Use trust_remote_code=True for datasets with custom scripts
            dataset = load_dataset("Skylion007/openwebtext", streaming=True, split="train", trust_remote_code=True)
            
            collected_texts = []
            processed_count = 0
            
            for example in dataset:
                processed_count += 1
                
                # Sample based on ratio
                if random.random() > sample_ratio:
                    continue
                    
                text = example['text']
                
                # Apply quality filtering
                if not self.quality_filter(text):
                    continue
                    
                collected_texts.append(text)
                
                # Check size limit
                estimated_size = sum(self.estimate_text_size_mb(t) for t in collected_texts)
                if estimated_size >= self.target_size_mb * 0.6:  # 60% of target for OpenWebText
                    break
                    
                # Progress logging
                if len(collected_texts) % 1000 == 0:
                    self.logger.log(f"  Collected {len(collected_texts)} texts, "
                                  f"processed {processed_count} examples, "
                                  f"estimated size: {estimated_size:.1f}MB")
            
            self.logger.log(f"✅ OpenWebText: Collected {len(collected_texts)} texts")
            return collected_texts
            
        except Exception as e:
            self.logger.log(f"❌ Error downloading OpenWebText: {e}")
            return []
    
    def download_wikipedia_subset(self, max_articles: int = 10000) -> List[str]:
        """Download and process Wikipedia subset."""
        if not DATASETS_AVAILABLE:
            self.logger.log("❌ Datasets library not available. Skipping Wikipedia.")
            return []

        self.logger.log(f"📥 Downloading Wikipedia subset (max articles: {max_articles})")

        try:
            # Use trust_remote_code=True for datasets with custom scripts
            dataset = load_dataset("wikipedia", "20220301.en", streaming=True, split="train", trust_remote_code=True)
            
            collected_texts = []
            processed_count = 0
            
            for article in dataset:
                processed_count += 1
                
                text = article['text']
                
                # Filter by length (medium-length articles)
                if len(text) < 500 or len(text) > 15000:
                    continue
                    
                # Apply quality filtering
                if not self.quality_filter(text):
                    continue
                    
                collected_texts.append(text)
                
                # Check limits
                if len(collected_texts) >= max_articles:
                    break
                    
                estimated_size = sum(self.estimate_text_size_mb(t) for t in collected_texts)
                if estimated_size >= self.target_size_mb * 0.4:  # 40% of target for Wikipedia
                    break
                    
                # Progress logging
                if len(collected_texts) % 500 == 0:
                    self.logger.log(f"  Collected {len(collected_texts)} articles, "
                                  f"processed {processed_count} articles, "
                                  f"estimated size: {estimated_size:.1f}MB")
            
            self.logger.log(f"✅ Wikipedia: Collected {len(collected_texts)} articles")
            return collected_texts
            
        except Exception as e:
            self.logger.log(f"❌ Error downloading Wikipedia: {e}")
            return []
    
    def save_expanded_dataset(self, texts: List[str], filename: str = "expanded_english_corpus.txt"):
        """Save the expanded dataset to file."""
        output_path = self.output_dir / filename
        
        self.logger.log(f"💾 Saving expanded dataset to {output_path}")
        
        with open(output_path, 'w', encoding='utf-8') as f:
            for i, text in enumerate(texts):
                f.write(text)
                f.write('\n\n')  # Separate texts with double newline
                
                if (i + 1) % 1000 == 0:
                    self.logger.log(f"  Saved {i + 1}/{len(texts)} texts")
        
        # Calculate final size
        file_size_mb = output_path.stat().st_size / (1024 * 1024)
        self.logger.log(f"✅ Saved {len(texts)} texts, total size: {file_size_mb:.1f}MB")
        
        return output_path
    
    def create_metadata(self, texts: List[str], sources: Dict[str, int], output_path: Path):
        """Create metadata file for the expanded dataset."""
        metadata = {
            "total_texts": len(texts),
            "total_size_mb": output_path.stat().st_size / (1024 * 1024),
            "sources": sources,
            "average_text_length": sum(len(text) for text in texts) / len(texts) if texts else 0,
            "quality_filters_applied": [
                "length_filter_100_50000",
                "word_density_filter",
                "repetition_filter",
                "line_break_filter"
            ]
        }
        
        metadata_path = output_path.parent / f"{output_path.stem}_metadata.json"
        with open(metadata_path, 'w') as f:
            json.dump(metadata, f, indent=2)
            
        self.logger.log(f"📊 Metadata saved to {metadata_path}")
        return metadata_path
    
    def run_expansion(self) -> Path:
        """Run the complete data expansion pipeline."""
        self.logger.log("🚀 Starting data expansion pipeline")
        self.logger.log(f"Target size: {self.target_size_mb}MB")
        
        all_texts = []
        sources = {}
        
        # Download OpenWebText subset
        openwebtext_texts = self.download_openwebtext_subset()
        all_texts.extend(openwebtext_texts)
        sources["openwebtext"] = len(openwebtext_texts)
        
        # Download Wikipedia subset
        wikipedia_texts = self.download_wikipedia_subset()
        all_texts.extend(wikipedia_texts)
        sources["wikipedia"] = len(wikipedia_texts)
        
        if not all_texts:
            self.logger.log("❌ No texts collected. Expansion failed.")
            return None
        
        # Shuffle for better distribution
        random.shuffle(all_texts)
        
        # Save dataset
        output_path = self.save_expanded_dataset(all_texts)
        
        # Create metadata
        self.create_metadata(all_texts, sources, output_path)
        
        self.logger.log("🎉 Data expansion completed successfully!")
        return output_path


def main():
    """Main function for command-line usage."""
    parser = argparse.ArgumentParser(description="Expand English dataset for language model training")
    parser.add_argument("--output-dir", default="data/raw/expanded_english",
                       help="Output directory for expanded dataset")
    parser.add_argument("--target-size-mb", type=int, default=1000,
                       help="Target dataset size in MB")
    parser.add_argument("--openwebtext-ratio", type=float, default=0.001,
                       help="Sampling ratio for OpenWebText")
    parser.add_argument("--wikipedia-articles", type=int, default=10000,
                       help="Maximum number of Wikipedia articles")
    
    args = parser.parse_args()
    
    # Initialize logger
    logger = SimpleLogger()
    
    # Create pipeline
    pipeline = DataExpansionPipeline(
        output_dir=args.output_dir,
        target_size_mb=args.target_size_mb,
        logger=logger
    )
    
    # Run expansion
    output_path = pipeline.run_expansion()
    
    if output_path:
        logger.log(f"✅ Expanded dataset saved to: {output_path}")
        logger.log("Next steps:")
        logger.log("1. Run preprocessing on the expanded dataset")
        logger.log("2. Retrain tokenizer with larger vocabulary")
        logger.log("3. Update model configuration for larger dataset")
        logger.log("4. Start training with enhanced configuration")
    else:
        logger.log("❌ Data expansion failed")
        sys.exit(1)


if __name__ == "__main__":
    main()
