#!/usr/bin/env python3
"""
Kaggle Training Setup Script
This script prepares everything needed for fast GPU training on Kaggle Notebooks.
"""

import os
import sys
import json
import zipfile
import shutil
from pathlib import Path

def create_kaggle_package():
    """Create a zip package with all necessary files for Kaggle training."""
    
    print("🚀 Creating Kaggle training package...")
    
    # Create temporary directory for packaging
    package_dir = Path("kaggle_package")
    if package_dir.exists():
        shutil.rmtree(package_dir)
    package_dir.mkdir()
    
    # Copy source code
    src_dirs = ["src", "scripts"]
    for src_dir in src_dirs:
        if Path(src_dir).exists():
            shutil.copytree(src_dir, package_dir / src_dir)
            print(f"✅ Copied {src_dir}/")
    
    # Copy processed data
    data_dirs = ["data/processed", "data/vocab"]
    for data_dir in data_dirs:
        if Path(data_dir).exists():
            dest_dir = package_dir / data_dir
            dest_dir.parent.mkdir(parents=True, exist_ok=True)
            shutil.copytree(data_dir, dest_dir)
            print(f"✅ Copied {data_dir}/")
    
    # Create requirements.txt for Kaggle
    requirements_path = package_dir / "requirements.txt"
    requirements = [
        "torch>=2.0.0",
        "datasets>=2.0.0",
        "transformers>=4.20.0",
        "numpy>=1.21.0",
        "matplotlib>=3.5.0",
        "tqdm>=4.64.0"
    ]
    with open(requirements_path, 'w') as f:
        f.write('\n'.join(requirements))
    print("✅ Created requirements.txt")
    
    # Create Kaggle training script
    create_kaggle_training_script(package_dir)
    
    # Create zip file
    zip_path = "ai_training_kaggle.zip"
    with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for root, dirs, files in os.walk(package_dir):
            for file in files:
                file_path = Path(root) / file
                arcname = file_path.relative_to(package_dir)
                zipf.write(file_path, arcname)
    
    # Cleanup
    shutil.rmtree(package_dir)
    
    print(f"🎉 Kaggle package created: {zip_path}")
    print(f"📦 Package size: {Path(zip_path).stat().st_size / (1024*1024):.1f} MB")
    
    return zip_path

def create_kaggle_training_script(package_dir):
    """Create the main training script for Kaggle."""
    
    kaggle_script = '''#!/usr/bin/env python3
"""
Kaggle GPU Training Script for Enhanced AI Model
Run this in Kaggle Notebooks for reliable GPU training!
"""

import os
import sys
import torch
import json
from pathlib import Path
from torch.utils.data import DataLoader

# GPU Setup
print("🚀 Setting up Kaggle GPU training environment...")
print(f"PyTorch version: {torch.__version__}")
print(f"CUDA available: {torch.cuda.is_available()}")

if torch.cuda.is_available():
    print(f"🔥 GPU: {torch.cuda.get_device_name()}")
    print(f"🔥 GPU Memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB")
    device = torch.device("cuda")
else:
    print("❌ GPU not available! Make sure accelerator is set to GPU")
    device = torch.device("cpu")

# Import our modules
sys.path.append('/kaggle/working')
from src.model.transformer import Transformer
from src.preprocessing.improved_tokenizer import ImprovedBPETokenizer
from src.preprocessing.data_loaders import TextDataset, collate_batch
from src.config.model_config import ModelConfig, TrainingConfig, DataConfig
from src.utils.logging import SimpleLogger

def train_kaggle_gpu(model, dataloader, optimizer, criterion, config, logger, device):
    """Kaggle-optimized GPU training loop."""
    model.train()
    scaler = torch.amp.GradScaler('cuda')
    
    for epoch in range(config.num_epochs):
        total_loss = 0
        num_batches = 0
        
        logger.log(f"🔥 Epoch {epoch+1}/{config.num_epochs}")
        
        for batch_idx, (inputs, targets) in enumerate(dataloader):
            inputs, targets = inputs.to(device), targets.to(device)
            
            # Create causal mask for decoder
            seq_len = inputs.size(1)
            tgt_mask = torch.tril(torch.ones(seq_len, seq_len)).to(device)
            
            # Gradient accumulation
            if batch_idx % config.gradient_accumulation_steps == 0:
                optimizer.zero_grad()
            
            # Mixed precision forward pass
            with torch.autocast(device_type='cuda', dtype=torch.float16):
                outputs = model(inputs, inputs, src_mask=None, tgt_mask=tgt_mask)
                loss = criterion(outputs.view(-1, outputs.size(-1)), targets.view(-1))
                # Scale loss for gradient accumulation
                loss = loss / config.gradient_accumulation_steps
            
            # Mixed precision backward pass
            scaler.scale(loss).backward()
            
            # Step optimizer every gradient_accumulation_steps
            if (batch_idx + 1) % config.gradient_accumulation_steps == 0:
                scaler.step(optimizer)
                scaler.update()
            
            total_loss += loss.item() * config.gradient_accumulation_steps
            num_batches += 1
            
            # Log progress
            if batch_idx % config.log_interval == 0:
                logger.log(f"  Batch {batch_idx}, Loss: {loss.item() * config.gradient_accumulation_steps:.4f}")
            
            # Clear cache periodically
            if batch_idx % 100 == 0:
                torch.cuda.empty_cache()
        
        avg_loss = total_loss / num_batches
        logger.log(f"✅ Epoch {epoch+1} completed! Average Loss: {avg_loss:.4f}")
        
        # Save checkpoint
        if (epoch + 1) % config.checkpoint_interval == 0:
            checkpoint_path = f"/kaggle/working/checkpoints/kaggle_checkpoint_epoch_{epoch+1}.pth"
            torch.save(model.state_dict(), checkpoint_path)
            logger.log(f"💾 Checkpoint saved: {checkpoint_path}")
            
        # Clear cache after each epoch
        torch.cuda.empty_cache()
    
    logger.log("🎉 Kaggle GPU training completed!")

def main():
    """Main training function optimized for Kaggle."""
    
    # Balanced configuration for Kaggle GPU
    model_config = ModelConfig(
        vocab_size=16000,  # Will be updated from tokenizer
        embed_dim=384,     # Balanced size
        num_layers=6,      # Good balance of power and memory
        num_heads=6,       # Balanced attention
        ff_dim=1536,       # Balanced feed-forward
        max_seq_len=384,   # Good context length
        dropout=0.1
    )
    
    training_config = TrainingConfig(
        batch_size=16,     # Good batch size for Kaggle
        learning_rate=1e-4,
        num_epochs=12,     # Good number of epochs
        log_interval=50,   # Frequent logging
        checkpoint_interval=2, # Save every 2 epochs
        gradient_accumulation_steps=2  # Simulate larger batch
    )
    
    data_config = DataConfig(
        processed_data_dir="data/processed/english_tokens",
        vocab_dir="data/vocab"
    )
    
    # Setup device
    print(f"🔥 Using device: {device}")
    
    # Create checkpoint directory
    os.makedirs("/kaggle/working/checkpoints", exist_ok=True)
    
    # Initialize logger
    logger = SimpleLogger()
    
    # Load tokenizer
    logger.log("📚 Loading tokenizer...")
    tokenizer = ImprovedBPETokenizer()
    vocab_path = os.path.join(data_config.vocab_dir, "tokenizer_model.json")
    merges_path = os.path.join(data_config.vocab_dir, "merges.json")
    
    if not os.path.exists(vocab_path):
        logger.log("❌ Tokenizer not found! Please upload data files.")
        return
    
    tokenizer.load_from_json(vocab_path, merges_path)
    model_config.vocab_size = len(tokenizer.vocab)
    logger.log(f"✅ Loaded tokenizer with vocab size: {model_config.vocab_size}")
    
    # Load dataset
    logger.log("📊 Loading dataset...")
    tokenized_file_path = os.path.join(data_config.processed_data_dir, "tokenized_english_corpus.json")
    
    if not os.path.exists(tokenized_file_path):
        logger.log("❌ Tokenized data not found! Please upload data files.")
        return
    
    dataset = TextDataset(tokenized_file_path)
    train_dataloader = DataLoader(
        dataset, 
        batch_size=training_config.batch_size, 
        shuffle=True, 
        collate_fn=lambda b: collate_batch(b, tokenizer.vocab[tokenizer.pad_token], model_config.max_seq_len),
        num_workers=2
    )
    
    logger.log(f"✅ Loaded dataset with {len(dataset)} samples for Kaggle training")
    
    # Initialize model
    logger.log("🧠 Initializing balanced model...")
    model = Transformer(
        vocab_size=model_config.vocab_size,
        embed_dim=model_config.embed_dim,
        num_layers=model_config.num_layers,
        num_heads=model_config.num_heads,
        ff_dim=model_config.ff_dim,
        max_seq_len=model_config.max_seq_len,
        dropout=model_config.dropout
    ).to(device)
    
    # Count parameters
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    logger.log(f"📊 Model parameters: {total_params:,} total, {trainable_params:,} trainable")
    
    # Training setup
    optimizer = torch.optim.AdamW(model.parameters(), lr=training_config.learning_rate)
    criterion = torch.nn.CrossEntropyLoss(ignore_index=tokenizer.vocab[tokenizer.pad_token])
    
    # Start training
    logger.log("🔥 Starting Kaggle GPU training...")
    train_kaggle_gpu(model, train_dataloader, optimizer, criterion, training_config, logger, device)
    
    logger.log("🎉 Training completed!")
    logger.log("💾 Model checkpoints saved in '/kaggle/working/checkpoints/' directory")
    
    # Test the model
    logger.log("🧪 Testing improved model...")
    test_prompts = [
        "The weather today is",
        "Artificial intelligence will",
        "In the future, humans",
        "Technology has changed",
        "The most important thing"
    ]
    
    for prompt in test_prompts:
        try:
            input_ids = tokenizer.encode(prompt)
            input_tensor = torch.tensor([input_ids]).to(device)
            
            model.eval()
            with torch.no_grad():
                output = model(input_tensor, input_tensor)
                predicted_ids = torch.argmax(output[0], dim=-1)
                generated_text = tokenizer.decode(predicted_ids.cpu().tolist())
                
            logger.log(f"Prompt: '{prompt}' -> '{generated_text[:100]}...'")
        except Exception as e:
            logger.log(f"Error testing prompt '{prompt}': {e}")

if __name__ == "__main__":
    main()
'''
    
    script_path = package_dir / "kaggle_train.py"
    with open(script_path, 'w') as f:
        f.write(kaggle_script)
    
    print("✅ Created kaggle_train.py")

if __name__ == "__main__":
    # Check if preprocessing is complete
    if not Path("data/processed/english_tokens/tokenized_english_corpus.json").exists():
        print("❌ Preprocessing not complete. Please run scripts/preprocess_data.py first.")
        sys.exit(1)
    
    # Create Kaggle package
    zip_path = create_kaggle_package()
    
    print("\n🎉 Kaggle setup complete!")
    print("\n📋 Next steps:")
    print("1. Go to https://www.kaggle.com/code")
    print("2. Create a new notebook")
    print("3. Upload ai_training_kaggle.zip as a dataset")
    print("4. Set accelerator to GPU")
    print("5. Extract and run the training!")
    print("\n💡 Kaggle gives you 30 hours/week of FREE GPU time!")
