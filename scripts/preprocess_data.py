import os
from datasets import load_dataset
from src.preprocessing.tokenizer import BPETokenizer # Import the tokenizer

def download_english_corpus(output_dir="data/raw/english_corpus", num_samples=1000):
    """
    Downloads a portion of an English language corpus.
    """
    os.makedirs(output_dir, exist_ok=True)
    
    print(f"Downloading a subset of the 'wikitext-103-raw-v1' dataset...")
    # Load a smaller subset for initial development
    dataset = load_dataset("wikitext", "wikitext-103-raw-v1", split=f"train[:{num_samples}]")

    output_file = os.path.join(output_dir, "wikitext_subset.txt")
    with open(output_file, "w", encoding="utf-8") as f:
        for entry in dataset:
            text = entry["text"].strip()
            if text:
                f.write(text + "\n")
    print(f"Downloaded {num_samples} samples to {output_file}")
    return output_file # Return the path to the downloaded file

def preprocess_and_tokenize_english_corpus(raw_data_path, processed_dir="data/processed/english_tokens", vocab_dir="data/vocab", vocab_size=1000):
    """
    Reads raw text, trains a BPE tokenizer, and tokenizes the text.
    """
    os.makedirs(processed_dir, exist_ok=True)
    os.makedirs(vocab_dir, exist_ok=True)

    print(f"Reading raw text from {raw_data_path}...")
    with open(raw_data_path, "r", encoding="utf-8") as f:
        texts = f.readlines()
    # Filter out empty lines and newlines for cleaner training
    texts = [line.strip() for line in texts if line.strip()]

    print(f"Training BPE tokenizer with vocab size {vocab_size}...")
    tokenizer = BPETokenizer()
    tokenizer.train(texts, vocab_size=vocab_size)
    tokenizer.save_tokenizer(os.path.join(vocab_dir, "tokenizer_model.json"), os.path.join(vocab_dir, "merges.json"))

    print("Tokenizing raw text...")
    tokenized_data = []
    for i, text in enumerate(texts):
        if i % 100 == 0: # Print progress
            print(f"  Tokenizing line {i}/{len(texts)}", end='\r')
        encoded_text = tokenizer.encode(text)
        tokenized_data.append(encoded_text)
    print("\nFinished tokenizing.")

    # Save tokenized data (e.g., as a list of lists of integers in a JSON or pickle file)
    # For simplicity, saving as a JSON list of lists for now. Consider more efficient formats later.
    tokenized_output_file = os.path.join(processed_dir, "tokenized_english_corpus.json")
    with open(tokenized_output_file, "w", encoding="utf-8") as f:
        import json
        json.dump(tokenized_data, f)
    print(f"Tokenized data saved to {tokenized_output_file}")

if __name__ == "__main__":
    raw_file = download_english_corpus(num_samples=50000) # Significantly more samples for better training
    preprocess_and_tokenize_english_corpus(raw_file, vocab_size=2000) # Larger vocab too 