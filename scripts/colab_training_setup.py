#!/usr/bin/env python3
"""
Google Colab Training Setup Script
This script prepares everything needed for fast GPU training on Google Colab.
"""

import os
import sys
import json
import zipfile
import shutil
from pathlib import Path

def create_colab_package():
    """Create a zip package with all necessary files for Colab training."""
    
    print("🚀 Creating Colab training package...")
    
    # Create temporary directory for packaging
    package_dir = Path("colab_package")
    if package_dir.exists():
        shutil.rmtree(package_dir)
    package_dir.mkdir()
    
    # Copy source code
    src_dirs = ["src", "scripts"]
    for src_dir in src_dirs:
        if Path(src_dir).exists():
            shutil.copytree(src_dir, package_dir / src_dir)
            print(f"✅ Copied {src_dir}/")
    
    # Copy processed data
    data_dirs = ["data/processed", "data/vocab"]
    for data_dir in data_dirs:
        if Path(data_dir).exists():
            dest_dir = package_dir / data_dir
            dest_dir.parent.mkdir(parents=True, exist_ok=True)
            shutil.copytree(data_dir, dest_dir)
            print(f"✅ Copied {data_dir}/")
    
    # Copy configuration files
    config_files = ["requirements.txt", "README.md"]
    for config_file in config_files:
        if Path(config_file).exists():
            shutil.copy2(config_file, package_dir / config_file)
            print(f"✅ Copied {config_file}")
    
    # Create requirements.txt if it doesn't exist
    requirements_path = package_dir / "requirements.txt"
    if not requirements_path.exists():
        requirements = [
            "torch>=2.0.0",
            "datasets>=2.0.0",
            "transformers>=4.20.0",
            "numpy>=1.21.0",
            "matplotlib>=3.5.0",
            "tqdm>=4.64.0"
        ]
        with open(requirements_path, 'w') as f:
            f.write('\n'.join(requirements))
        print("✅ Created requirements.txt")
    
    # Create Colab training script
    create_colab_training_script(package_dir)
    
    # Create zip file
    zip_path = "ai_training_colab.zip"
    with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for root, dirs, files in os.walk(package_dir):
            for file in files:
                file_path = Path(root) / file
                arcname = file_path.relative_to(package_dir)
                zipf.write(file_path, arcname)
    
    # Cleanup
    shutil.rmtree(package_dir)
    
    print(f"🎉 Colab package created: {zip_path}")
    print(f"📦 Package size: {Path(zip_path).stat().st_size / (1024*1024):.1f} MB")
    
    return zip_path

def create_colab_training_script(package_dir):
    """Create the main training script for Colab."""
    
    colab_script = '''#!/usr/bin/env python3
"""
Google Colab Training Script for Enhanced AI Model
Run this in Google Colab for fast GPU training!
"""

import os
import sys
import torch
import json
from pathlib import Path
from torch.utils.data import DataLoader

# Setup
print("🚀 Setting up training environment...")
print(f"PyTorch version: {torch.__version__}")
print(f"CUDA available: {torch.cuda.is_available()}")
if torch.cuda.is_available():
    print(f"GPU: {torch.cuda.get_device_name()}")
    print(f"GPU Memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB")

# Import our modules
sys.path.append('.')
from src.model.transformer import Transformer
from src.preprocessing.improved_tokenizer import ImprovedBPETokenizer
from src.preprocessing.data_loaders import TextDataset, collate_batch
from src.training.trainer import Trainer
from src.config.model_config import ModelConfig, TrainingConfig, DataConfig
from src.utils.logging import SimpleLogger
from src.utils.checkpoint_manager import CheckpointManager

def main():
    """Main training function optimized for Colab."""
    
    # Configuration for Colab (optimized for GPU)
    model_config = ModelConfig(
        vocab_size=16000,  # Will be updated from tokenizer
        embed_dim=512,     # Larger for better performance
        num_layers=8,      # More layers for better learning
        num_heads=8,       # More attention heads
        ff_dim=2048,       # Larger feed-forward
        max_seq_len=512,   # Longer sequences
        dropout=0.1
    )
    
    training_config = TrainingConfig(
        batch_size=32,     # Larger batch size for GPU
        learning_rate=1e-4,
        num_epochs=10,     # More epochs for better training
        log_interval=100,
        checkpoint_interval=2,
        gradient_accumulation_steps=2
    )
    
    data_config = DataConfig(
        processed_data_dir="data/processed/english_tokens",
        vocab_dir="data/vocab",
        checkpoint_dir="checkpoints"
    )
    
    # Setup device (prefer CUDA for Colab)
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"🔧 Using device: {device}")
    
    # Initialize logger
    logger = SimpleLogger()
    
    # Load tokenizer
    logger.log("📚 Loading tokenizer...")
    tokenizer = ImprovedBPETokenizer()
    vocab_path = os.path.join(data_config.vocab_dir, "tokenizer_model.json")
    merges_path = os.path.join(data_config.vocab_dir, "merges.json")
    
    if not os.path.exists(vocab_path):
        logger.log("❌ Tokenizer not found! Please upload preprocessed data.")
        return
    
    tokenizer.load_from_json(vocab_path, merges_path)
    model_config.vocab_size = len(tokenizer.vocab)
    logger.log(f"✅ Loaded tokenizer with vocab size: {model_config.vocab_size}")
    
    # Load dataset
    logger.log("📊 Loading dataset...")
    tokenized_file_path = os.path.join(data_config.processed_data_dir, "tokenized_english_corpus.json")
    
    if not os.path.exists(tokenized_file_path):
        logger.log("❌ Tokenized data not found! Please upload preprocessed data.")
        return
    
    dataset = TextDataset(tokenized_file_path)
    train_dataloader = DataLoader(
        dataset, 
        batch_size=training_config.batch_size, 
        shuffle=True, 
        collate_fn=lambda b: collate_batch(b, tokenizer.vocab[tokenizer.pad_token], model_config.max_seq_len)
    )
    
    logger.log(f"✅ Loaded dataset with {len(dataset)} samples")
    
    # Initialize model
    logger.log("🧠 Initializing model...")
    model = Transformer(
        vocab_size=model_config.vocab_size,
        embed_dim=model_config.embed_dim,
        num_layers=model_config.num_layers,
        num_heads=model_config.num_heads,
        ff_dim=model_config.ff_dim,
        max_seq_len=model_config.max_seq_len,
        dropout=model_config.dropout
    )
    
    # Count parameters
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    logger.log(f"📊 Model parameters: {total_params:,} total, {trainable_params:,} trainable")
    
    # Initialize trainer
    trainer = Trainer(model, tokenizer, training_config, data_config, model_config, device, logger)
    trainer.update_scheduler_for_dataset_size(len(dataset))
    
    # Start training
    logger.log("🚀 Starting training...")
    trainer.train(train_dataloader)
    
    logger.log("🎉 Training completed!")
    logger.log("💾 Model checkpoints saved in 'checkpoints/' directory")
    
    # Test the model
    logger.log("🧪 Testing model...")
    test_prompts = [
        "The weather today is",
        "Artificial intelligence will",
        "In the future, humans",
        "Technology has changed",
        "The most important thing"
    ]
    
    for prompt in test_prompts:
        try:
            # Simple generation test
            input_ids = tokenizer.encode(prompt)
            input_tensor = torch.tensor([input_ids]).to(device)
            
            model.eval()
            with torch.no_grad():
                output = model(input_tensor, input_tensor)
                predicted_ids = torch.argmax(output[0], dim=-1)
                generated_text = tokenizer.decode(predicted_ids.cpu().tolist())
                
            logger.log(f"Prompt: '{prompt}' -> '{generated_text[:100]}...'")
        except Exception as e:
            logger.log(f"Error testing prompt '{prompt}': {e}")

if __name__ == "__main__":
    main()
'''
    
    script_path = package_dir / "colab_train.py"
    with open(script_path, 'w') as f:
        f.write(colab_script)
    
    print("✅ Created colab_train.py")

def create_colab_notebook():
    """Create a Jupyter notebook for easy Colab usage."""
    
    notebook = {
        "cells": [
            {
                "cell_type": "markdown",
                "metadata": {},
                "source": [
                    "# 🚀 Enhanced AI Training on Google Colab\n",
                    "\n",
                    "This notebook trains your improved AI model with the expanded dataset for better conversational abilities!\n",
                    "\n",
                    "## Setup Instructions:\n",
                    "1. Upload the `ai_training_colab.zip` file to this Colab session\n",
                    "2. Run all cells in order\n",
                    "3. Download the trained model when complete\n"
                ]
            },
            {
                "cell_type": "code",
                "execution_count": None,
                "metadata": {},
                "outputs": [],
                "source": [
                    "# 📦 Extract uploaded files\n",
                    "import zipfile\n",
                    "import os\n",
                    "\n",
                    "# Extract the training package\n",
                    "with zipfile.ZipFile('ai_training_colab.zip', 'r') as zip_ref:\n",
                    "    zip_ref.extractall('.')\n",
                    "\n",
                    "print('✅ Files extracted successfully!')\n",
                    "\n",
                    "# List contents\n",
                    "for root, dirs, files in os.walk('.'):\n",
                    "    level = root.replace('.', '').count(os.sep)\n",
                    "    indent = ' ' * 2 * level\n",
                    "    print(f'{indent}{os.path.basename(root)}/')\n",
                    "    subindent = ' ' * 2 * (level + 1)\n",
                    "    for file in files[:5]:  # Show first 5 files\n",
                    "        print(f'{subindent}{file}')\n",
                    "    if len(files) > 5:\n",
                    "        print(f'{subindent}... and {len(files)-5} more files')"
                ]
            },
            {
                "cell_type": "code",
                "execution_count": None,
                "metadata": {},
                "outputs": [],
                "source": [
                    "# 🔧 Install requirements\n",
                    "!pip install -r requirements.txt\n",
                    "\n",
                    "# Check GPU\n",
                    "import torch\n",
                    "print(f'CUDA available: {torch.cuda.is_available()}')\n",
                    "if torch.cuda.is_available():\n",
                    "    print(f'GPU: {torch.cuda.get_device_name()}')\n",
                    "    print(f'Memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB')"
                ]
            },
            {
                "cell_type": "code",
                "execution_count": None,
                "metadata": {},
                "outputs": [],
                "source": [
                    "# 🚀 Start training!\n",
                    "!python colab_train.py"
                ]
            },
            {
                "cell_type": "code",
                "execution_count": None,
                "metadata": {},
                "outputs": [],
                "source": [
                    "# 📦 Package trained model for download\n",
                    "import zipfile\n",
                    "import os\n",
                    "from pathlib import Path\n",
                    "\n",
                    "# Create download package\n",
                    "with zipfile.ZipFile('trained_model.zip', 'w') as zipf:\n",
                    "    # Add checkpoints\n",
                    "    if os.path.exists('checkpoints'):\n",
                    "        for root, dirs, files in os.walk('checkpoints'):\n",
                    "            for file in files:\n",
                    "                file_path = os.path.join(root, file)\n",
                    "                zipf.write(file_path, file_path)\n",
                    "    \n",
                    "    # Add tokenizer\n",
                    "    if os.path.exists('data/vocab'):\n",
                    "        for root, dirs, files in os.walk('data/vocab'):\n",
                    "            for file in files:\n",
                    "                file_path = os.path.join(root, file)\n",
                    "                zipf.write(file_path, file_path)\n",
                    "\n",
                    "print('✅ Trained model packaged as trained_model.zip')\n",
                    "print('📥 Download this file to get your trained model!')"
                ]
            }
        ],
        "metadata": {
            "kernelspec": {
                "display_name": "Python 3",
                "language": "python",
                "name": "python3"
            },
            "language_info": {
                "name": "python",
                "version": "3.8.0"
            }
        },
        "nbformat": 4,
        "nbformat_minor": 4
    }
    
    with open("AI_Training_Colab.ipynb", 'w') as f:
        json.dump(notebook, f, indent=2)
    
    print("✅ Created AI_Training_Colab.ipynb")

if __name__ == "__main__":
    # Check if preprocessing is complete
    if not Path("data/processed/english_tokens/tokenized_english_corpus.json").exists():
        print("❌ Preprocessing not complete. Please run scripts/preprocess_data.py first.")
        sys.exit(1)
    
    # Create Colab package
    zip_path = create_colab_package()
    
    # Create Colab notebook
    create_colab_notebook()
    
    print("\n🎉 Colab setup complete!")
    print("\n📋 Next steps:")
    print("1. Upload ai_training_colab.zip to Google Colab")
    print("2. Open AI_Training_Colab.ipynb in Colab")
    print("3. Run all cells to train your improved AI!")
    print("4. Download the trained model when complete")
    print("\n💡 Tip: Use Colab Pro for faster T4/V100 GPUs!")
