#!/usr/bin/env python3
"""
Google Colab Training Setup Script
This script prepares everything needed for fast GPU training on Google Colab.
"""

import os
import sys
import json
import zipfile
import shutil
from pathlib import Path

def create_colab_package():
    """Create a zip package with all necessary files for Colab training."""
    
    print("🚀 Creating Colab training package...")
    
    # Create temporary directory for packaging
    package_dir = Path("colab_package")
    if package_dir.exists():
        shutil.rmtree(package_dir)
    package_dir.mkdir()
    
    # Copy source code
    src_dirs = ["src", "scripts"]
    for src_dir in src_dirs:
        if Path(src_dir).exists():
            shutil.copytree(src_dir, package_dir / src_dir)
            print(f"✅ Copied {src_dir}/")
    
    # Copy processed data
    data_dirs = ["data/processed", "data/vocab"]
    for data_dir in data_dirs:
        if Path(data_dir).exists():
            dest_dir = package_dir / data_dir
            dest_dir.parent.mkdir(parents=True, exist_ok=True)
            shutil.copytree(data_dir, dest_dir)
            print(f"✅ Copied {data_dir}/")
    
    # Copy configuration files
    config_files = ["requirements.txt", "README.md"]
    for config_file in config_files:
        if Path(config_file).exists():
            shutil.copy2(config_file, package_dir / config_file)
            print(f"✅ Copied {config_file}")
    
    # Create requirements.txt if it doesn't exist
    requirements_path = package_dir / "requirements.txt"
    if not requirements_path.exists():
        requirements = [
            "torch>=2.0.0",
            "torch_xla[tpu]",
            "datasets>=2.0.0",
            "transformers>=4.20.0",
            "numpy>=1.21.0",
            "matplotlib>=3.5.0",
            "tqdm>=4.64.0"
        ]
        with open(requirements_path, 'w') as f:
            f.write('\n'.join(requirements))
        print("✅ Created requirements.txt")
    
    # Create Colab training script
    create_colab_training_script(package_dir)
    
    # Create zip file
    zip_path = "ai_training_colab.zip"
    with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for root, dirs, files in os.walk(package_dir):
            for file in files:
                file_path = Path(root) / file
                arcname = file_path.relative_to(package_dir)
                zipf.write(file_path, arcname)
    
    # Cleanup
    shutil.rmtree(package_dir)
    
    print(f"🎉 Colab package created: {zip_path}")
    print(f"📦 Package size: {Path(zip_path).stat().st_size / (1024*1024):.1f} MB")
    
    return zip_path

def create_colab_training_script(package_dir):
    """Create the main training script for Colab."""
    
    colab_script = '''#!/usr/bin/env python3
"""
Google Colab TPU Training Script for Enhanced AI Model
Run this in Google Colab for ULTRA-FAST TPU training!
"""

import os
import sys
import torch
import json
from pathlib import Path

# TPU Setup
print("🚀 Setting up TPU training environment...")
print(f"PyTorch version: {torch.__version__}")

# Install and setup TPU
try:
    import torch_xla
    import torch_xla.core.xla_model as xm
    import torch_xla.distributed.parallel_loader as pl
    import torch_xla.utils.utils as xu
    print("✅ TPU libraries loaded successfully!")

    # Check TPU availability
    device = xm.xla_device()
    print(f"🔥 TPU device: {device}")
    print(f"🔥 TPU cores: {xm.xrt_world_size()}")

except ImportError:
    print("❌ TPU libraries not found. Installing...")
    os.system("pip install torch_xla[tpu] -f https://storage.googleapis.com/libtpu-releases/index.html")
    import torch_xla
    import torch_xla.core.xla_model as xm
    import torch_xla.distributed.parallel_loader as pl
    device = xm.xla_device()
    print(f"✅ TPU setup complete! Device: {device}")

# Import our modules
sys.path.append('.')
from src.model.transformer import Transformer
from src.preprocessing.improved_tokenizer import ImprovedBPETokenizer
from src.preprocessing.data_loaders import TextDataset
from src.config.model_config import ModelConfig, TrainingConfig, DataConfig
from src.utils.logging import SimpleLogger
from torch.utils.data import DataLoader

def collate_batch_tpu(batch, pad_token_id, max_seq_len):
    """TPU-optimized batch collation."""
    sequences = []
    for seq in batch:
        if len(seq) > max_seq_len:
            seq = seq[:max_seq_len]
        sequences.append(seq)

    # Pad sequences
    max_len = min(max_seq_len, max(len(seq) for seq in sequences))
    padded_sequences = []
    for seq in sequences:
        padded = seq + [pad_token_id] * (max_len - len(seq))
        padded_sequences.append(padded)

    # Convert to tensors
    inputs = torch.tensor(padded_sequences[:-1] if len(padded_sequences) > 1 else padded_sequences)
    targets = torch.tensor(padded_sequences[1:] if len(padded_sequences) > 1 else padded_sequences)

    return inputs, targets

def train_tpu(model, dataloader, optimizer, criterion, config, logger, device):
    """TPU-optimized training loop."""
    model.train()

    for epoch in range(config.num_epochs):
        total_loss = 0
        num_batches = 0

        logger.log(f"🔥 Epoch {epoch+1}/{config.num_epochs}")

        for batch_idx, (inputs, targets) in enumerate(dataloader):
            # Inputs and targets are already on TPU device

            # Create causal mask for decoder
            seq_len = inputs.size(1)
            tgt_mask = torch.tril(torch.ones(seq_len, seq_len)).to(device)

            optimizer.zero_grad()

            # Forward pass
            outputs = model(inputs, inputs, src_mask=None, tgt_mask=tgt_mask)
            loss = criterion(outputs.view(-1, outputs.size(-1)), targets.view(-1))

            # Backward pass
            loss.backward()

            # TPU step
            xm.optimizer_step(optimizer)

            total_loss += loss.item()
            num_batches += 1

            # Log progress
            if batch_idx % config.log_interval == 0:
                logger.log(f"  Batch {batch_idx}, Loss: {loss.item():.4f}")

        avg_loss = total_loss / num_batches
        logger.log(f"✅ Epoch {epoch+1} completed! Average Loss: {avg_loss:.4f}")

        # Save checkpoint
        if (epoch + 1) % config.checkpoint_interval == 0:
            checkpoint_path = f"checkpoints/tpu_checkpoint_epoch_{epoch+1}.pth"
            xm.save(model.state_dict(), checkpoint_path)
            logger.log(f"💾 Checkpoint saved: {checkpoint_path}")

    logger.log("🎉 TPU training completed!")

def main():
    """Main training function optimized for TPU."""

    # Configuration for TPU (optimized for ultra-fast training)
    model_config = ModelConfig(
        vocab_size=16000,  # Will be updated from tokenizer
        embed_dim=512,     # Optimized for TPU
        num_layers=6,      # Slightly fewer layers for faster TPU training
        num_heads=8,       # Good for TPU parallelization
        ff_dim=2048,       # TPU-friendly size
        max_seq_len=256,   # Shorter for faster TPU training
        dropout=0.1
    )

    training_config = TrainingConfig(
        batch_size=64,     # Larger batch size for TPU
        learning_rate=2e-4, # Slightly higher LR for TPU
        num_epochs=5,      # Fewer epochs since TPU is so fast
        log_interval=50,   # More frequent logging
        checkpoint_interval=1, # Save every epoch
        gradient_accumulation_steps=1  # TPU handles large batches well
    )

    data_config = DataConfig(
        processed_data_dir="data/processed/english_tokens",
        vocab_dir="data/vocab"
    )

    # Setup TPU device
    device = xm.xla_device()
    print(f"🔥 Using TPU device: {device}")

    # Create checkpoint directory
    os.makedirs("checkpoints", exist_ok=True)

    # Initialize logger
    logger = SimpleLogger()
    
    # Load tokenizer
    logger.log("📚 Loading tokenizer...")
    tokenizer = ImprovedBPETokenizer()
    vocab_path = os.path.join(data_config.vocab_dir, "tokenizer_model.json")
    merges_path = os.path.join(data_config.vocab_dir, "merges.json")
    
    if not os.path.exists(vocab_path):
        logger.log("❌ Tokenizer not found! Please upload preprocessed data.")
        return
    
    tokenizer.load_from_json(vocab_path, merges_path)
    model_config.vocab_size = len(tokenizer.vocab)
    logger.log(f"✅ Loaded tokenizer with vocab size: {model_config.vocab_size}")
    
    # Load dataset
    logger.log("📊 Loading dataset...")
    tokenized_file_path = os.path.join(data_config.processed_data_dir, "tokenized_english_corpus.json")
    
    if not os.path.exists(tokenized_file_path):
        logger.log("❌ Tokenized data not found! Please upload preprocessed data.")
        return
    
    dataset = TextDataset(tokenized_file_path)

    # TPU-optimized data loading
    train_dataloader = DataLoader(
        dataset,
        batch_size=training_config.batch_size,
        shuffle=True,
        collate_fn=lambda b: collate_batch_tpu(b, tokenizer.vocab[tokenizer.pad_token], model_config.max_seq_len),
        num_workers=0  # TPU works best with single worker
    )

    # Wrap dataloader for TPU
    train_dataloader = pl.ParallelLoader(train_dataloader, [device]).per_device_loader(device)

    logger.log(f"✅ Loaded dataset with {len(dataset)} samples for TPU training")
    
    # Initialize model for TPU
    logger.log("🧠 Initializing model for TPU...")
    model = Transformer(
        vocab_size=model_config.vocab_size,
        embed_dim=model_config.embed_dim,
        num_layers=model_config.num_layers,
        num_heads=model_config.num_heads,
        ff_dim=model_config.ff_dim,
        max_seq_len=model_config.max_seq_len,
        dropout=model_config.dropout
    ).to(device)  # Move to TPU

    # Count parameters
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    logger.log(f"📊 Model parameters: {total_params:,} total, {trainable_params:,} trainable")

    # TPU-optimized training setup
    optimizer = torch.optim.AdamW(model.parameters(), lr=training_config.learning_rate)
    criterion = torch.nn.CrossEntropyLoss(ignore_index=tokenizer.vocab[tokenizer.pad_token])

    # Start TPU training
    logger.log("🔥 Starting ultra-fast TPU training...")
    train_tpu(model, train_dataloader, optimizer, criterion, training_config, logger, device)
    
    logger.log("🎉 Training completed!")
    logger.log("💾 Model checkpoints saved in 'checkpoints/' directory")
    
    # Test the model
    logger.log("🧪 Testing model...")
    test_prompts = [
        "The weather today is",
        "Artificial intelligence will",
        "In the future, humans",
        "Technology has changed",
        "The most important thing"
    ]
    
    for prompt in test_prompts:
        try:
            # Simple generation test
            input_ids = tokenizer.encode(prompt)
            input_tensor = torch.tensor([input_ids]).to(device)
            
            model.eval()
            with torch.no_grad():
                output = model(input_tensor, input_tensor)
                predicted_ids = torch.argmax(output[0], dim=-1)
                generated_text = tokenizer.decode(predicted_ids.cpu().tolist())
                
            logger.log(f"Prompt: '{prompt}' -> '{generated_text[:100]}...'")
        except Exception as e:
            logger.log(f"Error testing prompt '{prompt}': {e}")

if __name__ == "__main__":
    main()
'''
    
    script_path = package_dir / "colab_train.py"
    with open(script_path, 'w') as f:
        f.write(colab_script)
    
    print("✅ Created colab_train.py")

def create_colab_notebook():
    """Create a Jupyter notebook for easy Colab usage."""
    
    notebook = {
        "cells": [
            {
                "cell_type": "markdown",
                "metadata": {},
                "source": [
                    "# 🔥 ULTRA-FAST TPU Training on Google Colab\n",
                    "\n",
                    "This notebook trains your improved AI model with TPU v2-8 for INSANELY FAST training!\n",
                    "\n",
                    "## Setup Instructions:\n",
                    "1. **IMPORTANT:** Change runtime to TPU v2-8 (Runtime → Change runtime type → TPU v2-8)\n",
                    "2. Upload the `ai_training_colab.zip` file to this Colab session\n",
                    "3. Run all cells in order\n",
                    "4. Download the trained model when complete\n",
                    "\n",
                    "## Expected Training Time: 5-15 minutes! 🚀\n"
                ]
            },
            {
                "cell_type": "code",
                "execution_count": None,
                "metadata": {},
                "outputs": [],
                "source": [
                    "# 📦 Extract uploaded files\n",
                    "import zipfile\n",
                    "import os\n",
                    "\n",
                    "# Extract the training package\n",
                    "with zipfile.ZipFile('ai_training_colab.zip', 'r') as zip_ref:\n",
                    "    zip_ref.extractall('.')\n",
                    "\n",
                    "print('✅ Files extracted successfully!')\n",
                    "\n",
                    "# List contents\n",
                    "for root, dirs, files in os.walk('.'):\n",
                    "    level = root.replace('.', '').count(os.sep)\n",
                    "    indent = ' ' * 2 * level\n",
                    "    print(f'{indent}{os.path.basename(root)}/')\n",
                    "    subindent = ' ' * 2 * (level + 1)\n",
                    "    for file in files[:5]:  # Show first 5 files\n",
                    "        print(f'{subindent}{file}')\n",
                    "    if len(files) > 5:\n",
                    "        print(f'{subindent}... and {len(files)-5} more files')"
                ]
            },
            {
                "cell_type": "code",
                "execution_count": None,
                "metadata": {},
                "outputs": [],
                "source": [
                    "# 🔥 Install TPU requirements\n",
                    "!pip install torch_xla[tpu] -f https://storage.googleapis.com/libtpu-releases/index.html\n",
                    "!pip install -r requirements.txt\n",
                    "\n",
                    "# Check TPU\n",
                    "import torch\n",
                    "import torch_xla\n",
                    "import torch_xla.core.xla_model as xm\n",
                    "print(f'PyTorch version: {torch.__version__}')\n",
                    "print(f'TPU available: {xm.xrt_world_size() > 0}')\n",
                    "if xm.xrt_world_size() > 0:\n",
                    "    print(f'🔥 TPU cores: {xm.xrt_world_size()}')\n",
                    "    print(f'🔥 TPU device: {xm.xla_device()}')\n",
                    "else:\n",
                    "    print('❌ TPU not detected! Make sure runtime is set to TPU v2-8')"
                ]
            },
            {
                "cell_type": "code",
                "execution_count": None,
                "metadata": {},
                "outputs": [],
                "source": [
                    "# 🔥 Start ULTRA-FAST TPU training!\n",
                    "!python colab_train.py"
                ]
            },
            {
                "cell_type": "code",
                "execution_count": None,
                "metadata": {},
                "outputs": [],
                "source": [
                    "# 📦 Package trained model for download\n",
                    "import zipfile\n",
                    "import os\n",
                    "from pathlib import Path\n",
                    "\n",
                    "# Create download package\n",
                    "with zipfile.ZipFile('trained_model.zip', 'w') as zipf:\n",
                    "    # Add checkpoints\n",
                    "    if os.path.exists('checkpoints'):\n",
                    "        for root, dirs, files in os.walk('checkpoints'):\n",
                    "            for file in files:\n",
                    "                file_path = os.path.join(root, file)\n",
                    "                zipf.write(file_path, file_path)\n",
                    "    \n",
                    "    # Add tokenizer\n",
                    "    if os.path.exists('data/vocab'):\n",
                    "        for root, dirs, files in os.walk('data/vocab'):\n",
                    "            for file in files:\n",
                    "                file_path = os.path.join(root, file)\n",
                    "                zipf.write(file_path, file_path)\n",
                    "\n",
                    "print('✅ Trained model packaged as trained_model.zip')\n",
                    "print('📥 Download this file to get your trained model!')"
                ]
            }
        ],
        "metadata": {
            "kernelspec": {
                "display_name": "Python 3",
                "language": "python",
                "name": "python3"
            },
            "language_info": {
                "name": "python",
                "version": "3.8.0"
            }
        },
        "nbformat": 4,
        "nbformat_minor": 4
    }
    
    with open("AI_Training_Colab.ipynb", 'w') as f:
        json.dump(notebook, f, indent=2)
    
    print("✅ Created AI_Training_Colab.ipynb")

if __name__ == "__main__":
    # Check if preprocessing is complete
    if not Path("data/processed/english_tokens/tokenized_english_corpus.json").exists():
        print("❌ Preprocessing not complete. Please run scripts/preprocess_data.py first.")
        sys.exit(1)
    
    # Create Colab package
    zip_path = create_colab_package()
    
    # Create Colab notebook
    create_colab_notebook()
    
    print("\n🎉 Colab setup complete!")
    print("\n📋 Next steps:")
    print("1. Upload ai_training_colab.zip to Google Colab")
    print("2. Open AI_Training_Colab.ipynb in Colab")
    print("3. Run all cells to train your improved AI!")
    print("4. Download the trained model when complete")
    print("\n💡 Tip: Use Colab Pro for faster T4/V100 GPUs!")
