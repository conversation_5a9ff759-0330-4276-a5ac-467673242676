#!/usr/bin/env python3
"""
Test script for the improved model with expanded dataset.
This will verify that everything is working before Cola<PERSON> training.
"""

import os
import sys
import torch
import json
from pathlib import Path

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

from src.preprocessing.improved_tokenizer import ImprovedBPETokenizer
from src.model.transformer import Transformer
from src.config.model_config import ModelConfig

def test_tokenizer():
    """Test the improved tokenizer."""
    print("🔤 Testing improved tokenizer...")
    
    # Load tokenizer
    tokenizer = ImprovedBPETokenizer()
    vocab_path = "data/vocab/tokenizer_model.json"
    merges_path = "data/vocab/merges.json"
    
    if not os.path.exists(vocab_path):
        print("❌ Tokenizer not found. Please run preprocessing first.")
        return False
    
    tokenizer.load_from_json(vocab_path, merges_path)
    print(f"✅ Loaded tokenizer with vocab size: {len(tokenizer.vocab)}")
    
    # Test encoding/decoding
    test_texts = [
        "The weather today is beautiful and sunny.",
        "Artificial intelligence will revolutionize the future.",
        "In the future, humans and AI will work together.",
        "Technology has changed how we communicate.",
        "The most important thing in life is happiness."
    ]
    
    print("\n🧪 Testing tokenization:")
    for text in test_texts:
        encoded = tokenizer.encode(text)
        decoded = tokenizer.decode(encoded)
        print(f"Original: {text}")
        print(f"Encoded:  {encoded[:10]}... ({len(encoded)} tokens)")
        print(f"Decoded:  {decoded}")
        print(f"Match:    {'✅' if text.lower().strip() == decoded.lower().strip() else '❌'}")
        print()
    
    return True

def test_model_initialization():
    """Test model initialization with new vocab size."""
    print("🧠 Testing model initialization...")
    
    # Load tokenizer to get vocab size
    tokenizer = ImprovedBPETokenizer()
    vocab_path = "data/vocab/tokenizer_model.json"
    merges_path = "data/vocab/merges.json"
    tokenizer.load_from_json(vocab_path, merges_path)
    
    # Create model config
    model_config = ModelConfig(
        vocab_size=len(tokenizer.vocab),
        embed_dim=256,  # Smaller for testing
        num_layers=4,   # Fewer layers for testing
        num_heads=4,
        ff_dim=1024,
        max_seq_len=256,
        dropout=0.1
    )
    
    # Initialize model
    model = Transformer(
        vocab_size=model_config.vocab_size,
        embed_dim=model_config.embed_dim,
        num_layers=model_config.num_layers,
        num_heads=model_config.num_heads,
        ff_dim=model_config.ff_dim,
        max_seq_len=model_config.max_seq_len,
        dropout=model_config.dropout
    )
    
    # Count parameters
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    
    print(f"✅ Model initialized successfully")
    print(f"📊 Vocabulary size: {model_config.vocab_size:,}")
    print(f"📊 Total parameters: {total_params:,}")
    print(f"📊 Trainable parameters: {trainable_params:,}")
    print(f"📊 Model size: ~{total_params * 4 / (1024*1024):.1f} MB")
    
    return model, tokenizer

def test_forward_pass(model, tokenizer):
    """Test a forward pass through the model."""
    print("\n🔄 Testing forward pass...")
    
    # Create test input
    test_text = "The weather today is"
    input_ids = tokenizer.encode(test_text)
    
    # Add batch dimension and convert to tensor
    input_tensor = torch.tensor([input_ids])
    
    # Forward pass
    model.eval()
    with torch.no_grad():
        try:
            output = model(input_tensor, input_tensor)
            print(f"✅ Forward pass successful")
            print(f"📊 Input shape: {input_tensor.shape}")
            print(f"📊 Output shape: {output.shape}")
            print(f"📊 Expected vocab size: {len(tokenizer.vocab)}")
            print(f"📊 Output vocab size: {output.shape[-1]}")
            
            # Test prediction
            predicted_ids = torch.argmax(output[0], dim=-1)
            predicted_text = tokenizer.decode(predicted_ids.tolist())
            print(f"🎯 Input: '{test_text}'")
            print(f"🎯 Predicted: '{predicted_text}'")
            
            return True
        except Exception as e:
            print(f"❌ Forward pass failed: {e}")
            return False

def test_dataset_loading():
    """Test loading the processed dataset."""
    print("\n📊 Testing dataset loading...")
    
    dataset_path = "data/processed/english_tokens/tokenized_english_corpus.json"
    if not os.path.exists(dataset_path):
        print("❌ Tokenized dataset not found. Please run preprocessing first.")
        return False
    
    with open(dataset_path, 'r') as f:
        tokenized_data = json.load(f)
    
    print(f"✅ Dataset loaded successfully")
    print(f"📊 Number of texts: {len(tokenized_data):,}")
    print(f"📊 Average tokens per text: {sum(len(tokens) for tokens in tokenized_data) / len(tokenized_data):.1f}")
    print(f"📊 Total tokens: {sum(len(tokens) for tokens in tokenized_data):,}")
    print(f"📊 Shortest text: {min(len(tokens) for tokens in tokenized_data)} tokens")
    print(f"📊 Longest text: {max(len(tokens) for tokens in tokenized_data)} tokens")
    
    return True

def main():
    """Run all tests."""
    print("🧪 Testing Improved AI Model Setup")
    print("=" * 50)
    
    # Test tokenizer
    if not test_tokenizer():
        print("❌ Tokenizer test failed. Exiting.")
        return
    
    # Test model initialization
    try:
        model, tokenizer = test_model_initialization()
    except Exception as e:
        print(f"❌ Model initialization failed: {e}")
        return
    
    # Test forward pass
    if not test_forward_pass(model, tokenizer):
        print("❌ Forward pass test failed.")
        return
    
    # Test dataset loading
    if not test_dataset_loading():
        print("❌ Dataset loading test failed.")
        return
    
    print("\n🎉 All tests passed!")
    print("\n✅ Your improved AI setup is ready!")
    print("\n📋 Next steps:")
    print("1. Run 'python scripts/colab_training_setup.py' to create Colab package")
    print("2. Upload the package to Google Colab for fast GPU training")
    print("3. Or run local training with 'python -m src.training.trainer'")
    
    # Show improvement comparison
    print("\n📈 Improvements over original model:")
    print(f"   • Vocabulary size: ~1,000 → {len(tokenizer.vocab):,} tokens")
    print(f"   • Dataset size: ~1,000 → 10,700 texts")
    print(f"   • Text quality: Basic → High-quality filtered content")
    print(f"   • Model capacity: Small → {sum(p.numel() for p in model.parameters()):,} parameters")

if __name__ == "__main__":
    main()
