import os
import sys
import json
from pathlib import Path

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

from src.preprocessing.improved_tokenizer import ImprovedBPETokenizer

def load_expanded_corpus(corpus_path="data/raw/expanded_english/expanded_english_corpus.txt"):
    """
    Load the expanded English corpus that was created by expand_dataset.py
    """
    print(f"Loading expanded corpus from {corpus_path}...")

    if not os.path.exists(corpus_path):
        print(f"❌ Expanded corpus not found at {corpus_path}")
        print("Please run scripts/expand_dataset.py first to create the expanded dataset.")
        return []

    texts = []
    with open(corpus_path, "r", encoding="utf-8") as f:
        content = f.read()
        # Split by double newlines (how the expanded dataset was saved)
        raw_texts = content.split('\n\n')

        for text in raw_texts:
            text = text.strip()
            if text and len(text) > 50:  # Filter very short texts
                texts.append(text)

    print(f"✅ Loaded {len(texts)} texts from expanded corpus")
    return texts

def preprocess_and_tokenize_expanded_corpus(texts, processed_dir="data/processed/english_tokens", vocab_dir="data/vocab", vocab_size=16000):
    """
    Train improved tokenizer on expanded corpus and tokenize the text.
    Uses larger vocabulary size for better performance.
    """
    os.makedirs(processed_dir, exist_ok=True)
    os.makedirs(vocab_dir, exist_ok=True)

    print(f"Training improved BPE tokenizer with vocab size {vocab_size}...")
    print(f"Training on {len(texts)} texts...")

    tokenizer = ImprovedBPETokenizer()
    tokenizer.train(texts, vocab_size=vocab_size)

    # Save tokenizer
    vocab_path = os.path.join(vocab_dir, "tokenizer_model.json")
    merges_path = os.path.join(vocab_dir, "merges.json")
    tokenizer.save_to_json(vocab_path, merges_path)
    print(f"✅ Tokenizer saved to {vocab_path} and {merges_path}")

    print("Tokenizing expanded corpus...")
    tokenized_data = []
    for i, text in enumerate(texts):
        if i % 500 == 0:  # Progress every 500 texts
            print(f"  Tokenizing text {i}/{len(texts)} ({i/len(texts)*100:.1f}%)", end='\r')

        # Add BOS and EOS tokens for better language modeling
        encoded_text = [tokenizer.vocab[tokenizer.bos_token]] + tokenizer.encode(text) + [tokenizer.vocab[tokenizer.eos_token]]
        tokenized_data.append(encoded_text)

    print(f"\n✅ Finished tokenizing {len(tokenized_data)} texts")

    # Save tokenized data
    tokenized_output_file = os.path.join(processed_dir, "tokenized_english_corpus.json")
    with open(tokenized_output_file, "w", encoding="utf-8") as f:
        json.dump(tokenized_data, f)
    print(f"✅ Tokenized data saved to {tokenized_output_file}")

    # Save metadata
    metadata = {
        "vocab_size": len(tokenizer.vocab),
        "num_texts": len(tokenized_data),
        "avg_tokens_per_text": sum(len(tokens) for tokens in tokenized_data) / len(tokenized_data),
        "total_tokens": sum(len(tokens) for tokens in tokenized_data),
        "special_tokens": tokenizer.special_tokens
    }

    metadata_path = os.path.join(processed_dir, "preprocessing_metadata.json")
    with open(metadata_path, "w", encoding="utf-8") as f:
        json.dump(metadata, f, indent=2)
    print(f"✅ Metadata saved to {metadata_path}")

    return tokenizer, tokenized_data

if __name__ == "__main__":
    # Load the expanded corpus
    texts = load_expanded_corpus()

    if texts:
        # Preprocess with larger vocabulary for better performance
        tokenizer, tokenized_data = preprocess_and_tokenize_expanded_corpus(
            texts,
            vocab_size=16000  # Much larger vocabulary for better coverage
        )

        print(f"\n🎉 Preprocessing completed successfully!")
        print(f"📊 Final statistics:")
        print(f"   - Vocabulary size: {len(tokenizer.vocab)}")
        print(f"   - Number of texts: {len(tokenized_data)}")
        print(f"   - Total tokens: {sum(len(tokens) for tokens in tokenized_data)}")
        print(f"   - Average tokens per text: {sum(len(tokens) for tokens in tokenized_data) / len(tokenized_data):.1f}")
        print(f"\n✅ Ready for training! You can now run the trainer or upload to Colab.")
    else:
        print("❌ No texts loaded. Please run scripts/expand_dataset.py first.")