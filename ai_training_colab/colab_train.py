#!/usr/bin/env python3
"""
Google Colab Training Script for Enhanced AI Model
Run this in Google Colab for fast GPU training!
"""

import os
import sys
import torch
import json
from pathlib import Path
from torch.utils.data import DataLoader

# Setup
print("🚀 Setting up training environment...")
print(f"PyTorch version: {torch.__version__}")
print(f"CUDA available: {torch.cuda.is_available()}")
if torch.cuda.is_available():
    print(f"GPU: {torch.cuda.get_device_name()}")
    print(f"GPU Memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB")

# Import our modules
sys.path.append('.')
from src.model.transformer import Transformer
from src.preprocessing.improved_tokenizer import ImprovedBPETokenizer
from src.preprocessing.data_loaders import TextDataset, collate_batch
from src.training.trainer import Trainer
from src.config.model_config import ModelConfig, TrainingConfig, DataConfig
from src.utils.logging import SimpleLogger
from src.utils.checkpoint_manager import CheckpointManager

def main():
    """Main training function optimized for Colab."""
    
    # Configuration for Colab (optimized for GPU)
    model_config = ModelConfig(
        vocab_size=16000,  # Will be updated from tokenizer
        embed_dim=512,     # Larger for better performance
        num_layers=8,      # More layers for better learning
        num_heads=8,       # More attention heads
        ff_dim=2048,       # Larger feed-forward
        max_seq_len=512,   # Longer sequences
        dropout=0.1
    )
    
    training_config = TrainingConfig(
        batch_size=32,     # Larger batch size for GPU
        learning_rate=1e-4,
        num_epochs=10,     # More epochs for better training
        warmup_steps=1000,
        gradient_accumulation_steps=2,
        checkpoint_interval=2,
        max_grad_norm=1.0
    )
    
    data_config = DataConfig(
        processed_data_dir="data/processed/english_tokens",
        vocab_dir="data/vocab",
        checkpoint_dir="checkpoints"
    )
    
    # Setup device (prefer CUDA for Colab)
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"🔧 Using device: {device}")
    
    # Initialize logger
    logger = SimpleLogger()
    
    # Load tokenizer
    logger.log("📚 Loading tokenizer...")
    tokenizer = ImprovedBPETokenizer()
    vocab_path = os.path.join(data_config.vocab_dir, "tokenizer_model.json")
    merges_path = os.path.join(data_config.vocab_dir, "merges.json")
    
    if not os.path.exists(vocab_path):
        logger.log("❌ Tokenizer not found! Please upload preprocessed data.")
        return
    
    tokenizer.load_from_json(vocab_path, merges_path)
    model_config.vocab_size = len(tokenizer.vocab)
    logger.log(f"✅ Loaded tokenizer with vocab size: {model_config.vocab_size}")
    
    # Load dataset
    logger.log("📊 Loading dataset...")
    tokenized_file_path = os.path.join(data_config.processed_data_dir, "tokenized_english_corpus.json")
    
    if not os.path.exists(tokenized_file_path):
        logger.log("❌ Tokenized data not found! Please upload preprocessed data.")
        return
    
    dataset = TextDataset(tokenized_file_path)
    train_dataloader = DataLoader(
        dataset, 
        batch_size=training_config.batch_size, 
        shuffle=True, 
        collate_fn=lambda b: collate_batch(b, tokenizer.vocab[tokenizer.pad_token], model_config.max_seq_len)
    )
    
    logger.log(f"✅ Loaded dataset with {len(dataset)} samples")
    
    # Initialize model
    logger.log("🧠 Initializing model...")
    model = Transformer(
        vocab_size=model_config.vocab_size,
        embed_dim=model_config.embed_dim,
        num_layers=model_config.num_layers,
        num_heads=model_config.num_heads,
        ff_dim=model_config.ff_dim,
        max_seq_len=model_config.max_seq_len,
        dropout=model_config.dropout
    )
    
    # Count parameters
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    logger.log(f"📊 Model parameters: {total_params:,} total, {trainable_params:,} trainable")
    
    # Initialize trainer
    trainer = Trainer(model, tokenizer, training_config, data_config, model_config, device, logger)
    trainer.update_scheduler_for_dataset_size(len(dataset))
    
    # Start training
    logger.log("🚀 Starting training...")
    trainer.train(train_dataloader)
    
    logger.log("🎉 Training completed!")
    logger.log("💾 Model checkpoints saved in 'checkpoints/' directory")
    
    # Test the model
    logger.log("🧪 Testing model...")
    test_prompts = [
        "The weather today is",
        "Artificial intelligence will",
        "In the future, humans",
        "Technology has changed",
        "The most important thing"
    ]
    
    for prompt in test_prompts:
        try:
            # Simple generation test
            input_ids = tokenizer.encode(prompt)
            input_tensor = torch.tensor([input_ids]).to(device)
            
            model.eval()
            with torch.no_grad():
                output = model(input_tensor, input_tensor)
                predicted_ids = torch.argmax(output[0], dim=-1)
                generated_text = tokenizer.decode(predicted_ids.cpu().tolist())
                
            logger.log(f"Prompt: '{prompt}' -> '{generated_text[:100]}...'")
        except Exception as e:
            logger.log(f"Error testing prompt '{prompt}': {e}")

if __name__ == "__main__":
    main()
