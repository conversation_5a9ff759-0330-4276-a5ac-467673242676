import torch
from torch.utils.data import Dataset, DataLoader
import json
import os
from torch.nn import functional as F

class TextDataset(Dataset):
    """
    A PyTorch Dataset for loading tokenized text data.
    """
    def __init__(self, tokenized_data_path: str):
        if not os.path.exists(tokenized_data_path):
            raise FileNotFoundError(f"Tokenized data file not found at: {tokenized_data_path}")
        
        with open(tokenized_data_path, 'r', encoding='utf-8') as f:
            self.tokenized_data = json.load(f)
        
        if not self.tokenized_data:
            raise ValueError(f"No tokenized data found in {tokenized_data_path}")

    def __len__(self):
        return len(self.tokenized_data)

    def __getitem__(self, idx):
        return torch.tensor(self.tokenized_data[idx], dtype=torch.long)

def collate_batch(batch: list[torch.Tensor], pad_token_id: int, max_sequence_length: int) -> tuple[torch.Tensor, torch.Tensor]:
    """
    Pads or truncates sequences in a batch to the specified maximum length.
    Also creates target sequences by shifting the input sequences.
    """
    padded_inputs = []
    for item in batch:
        # Truncate if sequence is longer than max_sequence_length
        if len(item) > max_sequence_length:
            item = item[:max_sequence_length]

        # Pad to max_sequence_length
        padding_needed = max_sequence_length - len(item)
        padded_item = F.pad(item, (0, padding_needed), "constant", pad_token_id)
        padded_inputs.append(padded_item)
    
    padded_inputs_tensor = torch.stack(padded_inputs)
    
    # Create target by shifting input.
    # The target for an input sequence [t1, t2, ..., tn] is [t2, ..., tn, pad_token_id]
    # This is for causal language modeling where we predict the next token.
    targets = torch.cat([
        padded_inputs_tensor[:, 1:], 
        torch.full((padded_inputs_tensor.size(0), 1), fill_value=pad_token_id, dtype=torch.long)
    ], dim=1)
    
    return padded_inputs_tensor, targets

# Example usage (for testing purposes)
if __name__ == "__main__":
    from src.config.model_config import DataConfig
    from src.preprocessing.tokenizer import BPETokenizer
    from src.config.model_config import ModelConfig

    # --- Re-run preprocessing to ensure we have fresh tokenized data and tokenizer for tests ---
    # This part would normally be run via scripts/preprocess_data.py
    
    # Simulate a small raw data file for testing data_loaders
    raw_data_dir = "data/raw/english_corpus"
    processed_data_dir = "data/processed/english_tokens"
    vocab_dir = "data/vocab"
    
    test_raw_file_path = os.path.join(raw_data_dir, "test_data_for_dataloader.txt")
    os.makedirs(raw_data_dir, exist_ok=True)
    with open(test_raw_file_path, "w", encoding="utf-8") as f:
        f.write("This is a short sentence.\n")
        f.write("Another slightly longer example, much longer than the others.\n")
        f.write("Short one.\n")
    print(f"Created test raw data at: {test_raw_file_path}")

    # Train and save a tokenizer instance
    test_texts = [
        "This is a short sentence.",
        "Another slightly longer example, much longer than the others.",
        "Short one."
    ]
    tokenizer = BPETokenizer()
    tokenizer.train(test_texts, vocab_size=100) # Small vocab for test
    tokenizer.save_tokenizer(os.path.join(vocab_dir, "tokenizer_model.json"), os.path.join(vocab_dir, "merges.json"))
    
    # Get pad_token_id. Assume <pad> or 0 is our pad_token. For BPE, it's typically an unused ID.
    pad_token_id = tokenizer.vocab.get("<pad>")
    if pad_token_id is None:
        pad_token_id = len(tokenizer.vocab)
        tokenizer.vocab["<pad>"] = pad_token_id
        tokenizer.inverse_vocab[pad_token_id] = "<pad>"
        print(f"Added <pad> token with ID: {pad_token_id}")

    # Simulate tokenizing the test raw data
    tokenized_test_data = []
    for text in test_texts:
        encoded_text = tokenizer.encode(text)
        tokenized_test_data.append(encoded_text)
    
    # Save the simulated tokenized data to a temporary file
    test_tokenized_file_path = os.path.join(processed_data_dir, "test_tokenized_data_for_dataloader.json")
    os.makedirs(processed_data_dir, exist_ok=True)
    with open(test_tokenized_file_path, "w", encoding="utf-8") as f:
        json.dump(tokenized_test_data, f)
    print(f"Created test tokenized data at: {test_tokenized_file_path}")

    # --- Actual Data Loader Testing ---

    model_cfg = ModelConfig() # Use default max_seq_len for testing
    data_cfg = DataConfig()
    dataset = TextDataset(tokenized_data_path=test_tokenized_file_path)
    
    # Create a DataLoader instance
    # Pass a lambda function to collate_fn to capture pad_token_id and max_sequence_length
    dataloader = DataLoader(dataset, batch_size=2, shuffle=True, 
                            collate_fn=lambda b: collate_batch(b, pad_token_id=pad_token_id, max_sequence_length=model_cfg.max_seq_len))

    print("\nIterating through DataLoader:")
    for batch_idx, (inputs, targets) in enumerate(dataloader):
        print(f"Batch {batch_idx+1}:")
        print(f"  Inputs shape: {inputs.shape}")
        print(f"  Targets shape: {targets.shape}")
        print(f"  Inputs: {inputs}")
        print(f"  Targets: {targets}")
        print(f"  Decoded Input (first sample): {tokenizer.decode(inputs[0].tolist())}")
        print(f"  Decoded Target (first sample): {tokenizer.decode(targets[0].tolist())}")

    print("DataLoader tests passed.")

    # Clean up test files (optional)
    # os.remove(test_raw_file_path)
    # os.remove(test_tokenized_file_path)
    # print("Cleaned up test files.") 