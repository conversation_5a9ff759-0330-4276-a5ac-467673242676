# src/evaluation/metrics.py

import torch

def calculate_accuracy(predictions: torch.Tensor, targets: torch.Tensor, ignore_index: int = -100) -> float:
    """
    Calculates the accuracy of predictions against target labels.

    Args:
        predictions (torch.Tensor): The predicted logits or class probabilities (batch_size, seq_len, vocab_size).
        targets (torch.Tensor): The true target labels (batch_size, seq_len).
        ignore_index (int): Label to ignore when calculating accuracy (e.g., padding token ID).

    Returns:
        float: The accuracy as a percentage.
    """
    # Get the predicted token ID by taking the argmax over the vocabulary dimension
    predicted_tokens = predictions.argmax(dim=-1)
    
    # Create a mask to exclude ignored indices (e.g., padding)
    active_elements = (targets != ignore_index)
    
    # Calculate correct predictions only for active elements
    correct_predictions = (predicted_tokens == targets) & active_elements
    
    # Calculate accuracy
    accuracy = correct_predictions.sum().item() / active_elements.sum().item()
    return accuracy

if __name__ == "__main__":
    # Example usage for testing
    
    # Simulate model outputs (logits) and target labels
    vocab_size = 100
    batch_size = 2
    seq_len = 5
    
    # Predictions: batch_size, seq_len, vocab_size
    # Example: model predicts token 10 at pos 0, token 20 at pos 1, etc.
    sample_predictions = torch.randn(batch_size, seq_len, vocab_size)

    # Targets: batch_size, seq_len
    # Example: true labels are token 10, 20, 30, etc.
    # Include some padding (e.g., pad_token_id = 0)
    pad_token_id = 0
    sample_targets = torch.tensor([
        [10, 20, 30, 40, pad_token_id],
        [50, 60, 70, pad_token_id, pad_token_id]
    ], dtype=torch.long)

    # Manually set some predictions to be correct to test accuracy calculation
    # For first sample:
    sample_predictions[0, 0, 10] = 100.0 # Make prediction for token 10 correct
    sample_predictions[0, 1, 20] = 100.0 # Make prediction for token 20 correct
    sample_predictions[0, 2, 30] = 100.0 # Make prediction for token 30 correct
    # For second sample:
    sample_predictions[1, 0, 50] = 100.0 # Make prediction for token 50 correct
    sample_predictions[1, 1, 60] = 100.0 # Make prediction for token 60 correct

    accuracy = calculate_accuracy(sample_predictions, sample_targets, ignore_index=pad_token_id)
    print(f"Calculated Accuracy: {accuracy:.4f}") # Expected: 5/7 correct = 0.7143

    # Test case: All correct, no padding
    predictions_all_correct = torch.randn(1, 3, vocab_size)
    targets_all_correct = torch.tensor([[5, 6, 7]], dtype=torch.long)
    predictions_all_correct[0, 0, 5] = 100.0
    predictions_all_correct[0, 1, 6] = 100.0
    predictions_all_correct[0, 2, 7] = 100.0
    accuracy_all_correct = calculate_accuracy(predictions_all_correct, targets_all_correct)
    print(f"Accuracy (all correct, no padding): {accuracy_all_correct:.4f}") # Expected: 1.0

    # Test case: All wrong, no padding
    predictions_all_wrong = torch.randn(1, 3, vocab_size)
    targets_all_wrong = torch.tensor([[1, 2, 3]], dtype=torch.long)
    accuracy_all_wrong = calculate_accuracy(predictions_all_wrong, targets_all_wrong)
    print(f"Accuracy (all wrong, no padding): {accuracy_all_wrong:.4f}") # Expected: ~0.0

    # Test case: All padding
    predictions_all_pad = torch.randn(1, 3, vocab_size)
    targets_all_pad = torch.tensor([[pad_token_id, pad_token_id, pad_token_id]], dtype=torch.long)
    accuracy_all_pad = calculate_accuracy(predictions_all_pad, targets_all_pad, ignore_index=pad_token_id)
    # Should handle division by zero if all are ignored. Let's make it return 0.0 or nan for this case.
    print(f"Accuracy (all padding): {accuracy_all_pad:.4f}") # Expected: 0.0 or nan or similar handled value

    print("Metrics tests passed.") 