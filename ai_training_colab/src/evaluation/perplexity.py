import torch
import math

def calculate_perplexity(nll_loss: float) -> float:
    """
    Calculates perplexity from negative log-likelihood loss.

    Perplexity is a measure of how well a probability distribution or language model
    predicts a sample. A low perplexity indicates a good prediction.

    Args:
        nll_loss (float): The negative log-likelihood loss (cross-entropy loss).

    Returns:
        float: The perplexity value.
    """
    return math.exp(nll_loss)

if __name__ == "__main__":
    # Example usage for testing

    # Simulate NLL loss values
    loss1 = 4.0
    loss2 = 2.5
    loss3 = 0.5
    loss_low = 0.01
    loss_high = 10.0

    perplexity1 = calculate_perplexity(loss1)
    perplexity2 = calculate_perplexity(loss2)
    perplexity3 = calculate_perplexity(loss3)
    perplexity_low = calculate_perplexity(loss_low)
    perplexity_high = calculate_perplexity(loss_high)

    print(f"Perplexity for loss {loss1}: {perplexity1:.4f}")
    print(f"Perplexity for loss {loss2}: {perplexity2:.4f}")
    print(f"Perplexity for loss {loss3}: {perplexity3:.4f}")
    print(f"Perplexity for low loss {loss_low}: {perplexity_low:.4f}")
    print(f"Perplexity for high loss {loss_high}: {perplexity_high:.4f}")

    # Edge case: zero loss (should be 1.0 perplexity)
    perplexity_zero_loss = calculate_perplexity(0.0)
    print(f"Perplexity for zero loss: {perplexity_zero_loss:.4f}")
    assert math.isclose(perplexity_zero_loss, 1.0), "Perplexity for zero loss should be 1.0"

    # Edge case: very small positive loss
    perplexity_tiny_loss = calculate_perplexity(1e-9)
    print(f"Perplexity for tiny loss: {perplexity_tiny_loss:.4f}")
    assert perplexity_tiny_loss > 1.0, "Perplexity for tiny positive loss should be > 1.0"

    print("Perplexity calculation tests passed.") 