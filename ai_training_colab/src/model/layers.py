import torch
import torch.nn as nn

class FeedForwardBlock(nn.Module):
    """
    A simple two-layer Feed-Forward Network with ReLU activation and Dropout.
    Used within Transformer Encoder/Decoder layers.
    """
    def __init__(self, embed_dim: int, ff_dim: int, dropout: float = 0.1):
        super().__init__()
        self.linear1 = nn.Linear(embed_dim, ff_dim)
        self.relu = nn.ReLU()
        self.dropout = nn.Dropout(dropout)
        self.linear2 = nn.Linear(ff_dim, embed_dim)

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        x = self.linear1(x)
        x = self.relu(x)
        x = self.dropout(x)
        x = self.linear2(x)
        return x

# Example usage (for testing purposes)
if __name__ == "__main__":
    embed_dim = 512
    ff_dim = 2048 # Typically 4 * embed_dim
    batch_size = 2
    seq_len = 10

    # Simulate input tensor
    input_tensor = torch.randn(batch_size, seq_len, embed_dim)

    # Create a FeedForwardBlock instance
    ff_block = FeedForwardBlock(embed_dim, ff_dim)

    # Test forward pass
    output = ff_block(input_tensor)
    print(f"FeedForwardBlock input shape: {input_tensor.shape}")
    print(f"FeedForwardBlock output shape: {output.shape}") # Expected: (2, 10, 512)

    # Verify that the output is different from input
    assert not torch.equal(input_tensor, output), "FeedForwardBlock did not modify input!"

    print("FeedForwardBlock tests passed.") 