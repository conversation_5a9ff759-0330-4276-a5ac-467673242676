import torch
import torch.nn as nn
from src.model.attention import MultiHeadSelfAttention
from src.model.layers import FeedForwardBlock
from src.model.embeddings import TokenEmbedding, PositionalEncoding

class EncoderLayer(nn.Module):
    """
    A single Transformer Encoder Layer, consisting of Multi-Head Self-Attention
    and a Feed-Forward Network, with residual connections and layer normalization.
    """
    def __init__(self, embed_dim: int, num_heads: int, ff_dim: int, dropout: float = 0.1):
        super().__init__()
        self.self_attention = MultiHeadSelfAttention(embed_dim, num_heads, dropout)
        self.norm1 = nn.LayerNorm(embed_dim)
        self.dropout1 = nn.Dropout(dropout)

        self.feed_forward = FeedForwardBlock(embed_dim, ff_dim, dropout)
        self.norm2 = nn.LayerNorm(embed_dim)
        self.dropout2 = nn.Dropout(dropout)

    def forward(self, x: torch.Tensor, mask: torch.Tensor = None) -> torch.Tensor:
        # Self-Attention sub-layer
        attn_output = self.self_attention(x, x, x, mask)
        x = self.norm1(x + self.dropout1(attn_output)) # Add & Norm

        # Feed-Forward sub-layer
        ff_output = self.feed_forward(x)
        x = self.norm2(x + self.dropout2(ff_output)) # Add & Norm
        return x

class DecoderLayer(nn.Module):
    """
    A single Transformer Decoder Layer, consisting of Masked Multi-Head Self-Attention,
    Encoder-Decoder Multi-Head Attention, and a Feed-Forward Network,
    with residual connections and layer normalization.
    """
    def __init__(self, embed_dim: int, num_heads: int, ff_dim: int, dropout: float = 0.1):
        super().__init__()
        self.masked_self_attention = MultiHeadSelfAttention(embed_dim, num_heads, dropout)
        self.norm1 = nn.LayerNorm(embed_dim)
        self.dropout1 = nn.Dropout(dropout)

        self.encoder_decoder_attention = MultiHeadSelfAttention(embed_dim, num_heads, dropout)
        self.norm2 = nn.LayerNorm(embed_dim)
        self.dropout2 = nn.Dropout(dropout)

        self.feed_forward = FeedForwardBlock(embed_dim, ff_dim, dropout)
        self.norm3 = nn.LayerNorm(embed_dim)
        self.dropout3 = nn.Dropout(dropout)

    def forward(self, x: torch.Tensor, encoder_output: torch.Tensor, src_mask: torch.Tensor = None, tgt_mask: torch.Tensor = None) -> torch.Tensor:
        # Masked Self-Attention sub-layer
        attn_output = self.masked_self_attention(x, x, x, tgt_mask)
        x = self.norm1(x + self.dropout1(attn_output))

        # Encoder-Decoder Attention sub-layer
        # Query comes from decoder, Key/Value come from encoder output
        attn_output = self.encoder_decoder_attention(x, encoder_output, encoder_output, src_mask)
        x = self.norm2(x + self.dropout2(attn_output))

        # Feed-Forward sub-layer
        ff_output = self.feed_forward(x)
        x = self.norm3(x + self.dropout3(ff_output))
        return x


class Transformer(nn.Module):
    """
    The full Transformer model, combining Encoder and Decoder stacks.
    """
    def __init__(self, vocab_size: int, embed_dim: int, num_layers: int, num_heads: int, ff_dim: int, max_seq_len: int, dropout: float = 0.1):
        super().__init__()
        self.token_embedding = TokenEmbedding(vocab_size, embed_dim)
        self.positional_encoding = PositionalEncoding(embed_dim, max_seq_len, dropout)

        self.encoder_layers = nn.ModuleList([
            EncoderLayer(embed_dim, num_heads, ff_dim, dropout) for _ in range(num_layers)
        ])
        self.decoder_layers = nn.ModuleList([
            DecoderLayer(embed_dim, num_heads, ff_dim, dropout) for _ in range(num_layers)
        ])

        self.output_linear = nn.Linear(embed_dim, vocab_size) # For language modeling output

    def encode(self, src: torch.Tensor, src_mask: torch.Tensor = None) -> torch.Tensor:
        src = self.token_embedding(src)
        src = self.positional_encoding(src)
        for layer in self.encoder_layers:
            src = layer(src, src_mask)
        return src

    def decode(self, tgt: torch.Tensor, encoder_output: torch.Tensor, src_mask: torch.Tensor = None, tgt_mask: torch.Tensor = None) -> torch.Tensor:
        tgt = self.token_embedding(tgt)
        tgt = self.positional_encoding(tgt)
        for layer in self.decoder_layers:
            tgt = layer(tgt, encoder_output, src_mask, tgt_mask)
        return tgt

    def forward(self, src: torch.Tensor, tgt: torch.Tensor, src_mask: torch.Tensor = None, tgt_mask: torch.Tensor = None) -> torch.Tensor:
        encoder_output = self.encode(src, src_mask)
        decoder_output = self.decode(tgt, encoder_output, src_mask, tgt_mask)
        output = self.output_linear(decoder_output)
        return output

# Example usage for testing
if __name__ == "__main__":
    vocab_size = 1000
    embed_dim = 256
    num_layers = 2
    num_heads = 4
    ff_dim = 1024
    max_seq_len = 50
    dropout = 0.1

    batch_size = 2
    src_seq_len = 15
    tgt_seq_len = 12

    # Instantiate the full Transformer model
    transformer_model = Transformer(vocab_size, embed_dim, num_layers, num_heads, ff_dim, max_seq_len, dropout)
    print(f"Transformer model initialized: {transformer_model}")

    # Simulate input data
    src_tokens = torch.randint(0, vocab_size, (batch_size, src_seq_len)) # Encoder input
    tgt_tokens = torch.randint(0, vocab_size, (batch_size, tgt_seq_len)) # Decoder input

    # Create masks
    # Source mask (for padding if necessary, here we assume no padding for simplicity)
    src_mask = None 
    
    # Target mask (causal mask for decoder self-attention)
    tgt_mask = torch.triu(torch.ones(tgt_seq_len, tgt_seq_len), diagonal=1).bool().unsqueeze(0).unsqueeze(0)
    tgt_mask = ~tgt_mask # Invert so 0s are masked, 1s are kept

    # Forward pass
    output = transformer_model(src_tokens, tgt_tokens, src_mask=src_mask, tgt_mask=tgt_mask)
    print(f"Transformer output shape: {output.shape}") # Expected: (batch_size, tgt_seq_len, vocab_size)

    assert output.shape == (batch_size, tgt_seq_len, vocab_size), "Transformer output shape mismatch!"
    print("Transformer model tests passed.") 