#!/usr/bin/env python3
"""
Test script for the enhanced training pipeline.
This script runs a minimal training session to verify all components work together.
"""

import os
import sys
import torch
from torch.utils.data import DataLoader

# Add the project root to the path
sys.path.append('.')

from src.model.transformer import Transformer
from src.preprocessing.improved_tokenizer import ImprovedBPETokenizer
from src.preprocessing.data_loaders import TextDataset, collate_batch
from src.training.trainer import Trainer
from src.config.model_config import ModelConfig, TrainingConfig, DataConfig
from src.utils.logging import SimpleLogger

def test_enhanced_training():
    """Test the enhanced training pipeline with minimal configuration."""
    
    print("🚀 Testing Enhanced Training Pipeline")
    print("=" * 50)
    
    # Use smaller configuration for testing
    model_config = ModelConfig(
        embed_dim=256,  # Smaller for testing
        num_layers=2,   # Smaller for testing
        num_heads=4,    # Smaller for testing
        ff_dim=1024,    # Smaller for testing
        max_seq_len=512 # Smaller for testing
    )
    
    training_config = TrainingConfig(
        batch_size=4,   # Very small for testing
        learning_rate=1e-4,
        num_epochs=2,   # Just 2 epochs for testing
        log_interval=5,
        checkpoint_interval=1,
        gradient_accumulation_steps=2
    )
    
    data_config = DataConfig(
        processed_data_dir="data/processed/english_tokens",
        vocab_dir="data/vocab"
    )
    
    # Determine device
    device = torch.device("mps" if torch.backends.mps.is_available() else "cuda" if torch.cuda.is_available() else "cpu")
    print(f"✓ Using device: {device}")
    
    # Initialize logger
    logger = SimpleLogger()
    logger.log("Starting enhanced training pipeline test")
    
    # Load tokenizer
    try:
        tokenizer = ImprovedBPETokenizer()
        # Load from JSON files
        vocab_path = os.path.join(data_config.vocab_dir, "tokenizer_model.json")
        merges_path = os.path.join(data_config.vocab_dir, "merges.json")
        tokenizer.load_from_json(vocab_path, merges_path)
        model_config.vocab_size = len(tokenizer.vocab)
        print(f"✓ Tokenizer loaded with vocab size: {model_config.vocab_size}")
    except FileNotFoundError as e:
        print(f"❌ Tokenizer files not found: {e}")
        return False
    
    # Create dataset and dataloader
    try:
        tokenized_file_path = os.path.join(data_config.processed_data_dir, "tokenized_english_corpus.json")
        dataset = TextDataset(tokenized_file_path)
        
        # Use only a small subset for testing
        subset_size = min(100, len(dataset))
        dataset.tokenized_data = dataset.tokenized_data[:subset_size]
        
        train_dataloader = DataLoader(
            dataset, 
            batch_size=training_config.batch_size, 
            shuffle=True, 
            collate_fn=lambda b: collate_batch(b, tokenizer.vocab[tokenizer.pad_token], model_config.max_seq_len)
        )
        print(f"✓ Dataset loaded with {len(dataset)} samples")
    except (FileNotFoundError, ValueError) as e:
        print(f"❌ Error loading dataset: {e}")
        return False
    
    # Initialize model
    try:
        model = Transformer(
            vocab_size=model_config.vocab_size,
            embed_dim=model_config.embed_dim,
            num_layers=model_config.num_layers,
            num_heads=model_config.num_heads,
            ff_dim=model_config.ff_dim,
            max_seq_len=model_config.max_seq_len,
            dropout=model_config.dropout
        )
        print(f"✓ Model initialized with {sum(p.numel() for p in model.parameters())} parameters")
    except Exception as e:
        print(f"❌ Error initializing model: {e}")
        return False
    
    # Initialize Trainer
    try:
        trainer = Trainer(model, tokenizer, training_config, data_config, model_config, device, logger)
        
        # Update scheduler with accurate dataset size
        trainer.update_scheduler_for_dataset_size(len(dataset))
        print("✓ Trainer initialized with enhanced scheduler")
    except Exception as e:
        print(f"❌ Error initializing trainer: {e}")
        return False
    
    # Run training test
    try:
        print("\n🏃 Starting training test...")
        trainer.train(train_dataloader)
        print("✓ Training completed successfully!")
        return True
    except Exception as e:
        print(f"❌ Error during training: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_enhanced_training()
    if success:
        print("\n🎉 Enhanced training pipeline test PASSED!")
        print("All components are working correctly with the improved configuration.")
    else:
        print("\n💥 Enhanced training pipeline test FAILED!")
        print("Please check the error messages above.")
    
    sys.exit(0 if success else 1)
