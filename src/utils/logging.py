import os
import time

class SimpleLogger:
    def __init__(self, log_dir="logs"):
        self.log_dir = log_dir
        os.makedirs(log_dir, exist_ok=True)
        self.log_file = os.path.join(log_dir, f"training_{int(time.time())}.log")

    def log(self, message: str):
        print(message)
        with open(self.log_file, "a", encoding="utf-8") as f:
            f.write(message + "\n")

    def get_log_file_path(self):
        return self.log_file

if __name__ == "__main__":
    logger = SimpleLogger()
    logger.log("This is a test log message.")
    logger.log("Another message.")
    print(f"Logs are being written to: {logger.get_log_file_path()}") 