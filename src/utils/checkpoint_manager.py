import torch
import os
from typing import Optional # Import Optional

# Assuming ImprovedBPETokenizer is available via a relative import or sys path modification
# For now, we'll keep it simple and assume it's resolvable in the actual runtime.
# If not, a circular import might occur, and we'd need to rethink the dependency.
# from src.preprocessing.improved_tokenizer import ImprovedBPETokenizer 

class CheckpointManager:
    """
    Manages saving and loading model checkpoints.
    """
    def __init__(self, checkpoint_dir: str = "checkpoints"):
        self.checkpoint_dir = checkpoint_dir
        os.makedirs(self.checkpoint_dir, exist_ok=True)

    def save_checkpoint(self, model: torch.nn.Module, optimizer: torch.optim.Optimizer, 
                        scheduler: torch.optim.lr_scheduler._LRScheduler, 
                        epoch: int, loss: float, vocab_size: int, phase: str = "phase1_english",
                        tokenizer=None, tokenizer_vocab_path: Optional[str] = None, tokenizer_merges_path: Optional[str] = None):
        """
        Saves the current state of the model, optimizer, scheduler, and optionally tokenizer.
        """
        phase_checkpoint_dir = os.path.join(self.checkpoint_dir, phase)
        os.makedirs(phase_checkpoint_dir, exist_ok=True)
        path = os.path.join(phase_checkpoint_dir, f"model_epoch_{epoch+1}.pt")

        checkpoint = {
            'epoch': epoch,
            'model_state_dict': model.state_dict(),
            'optimizer_state_dict': optimizer.state_dict(),
            'scheduler_state_dict': scheduler.state_dict(),
            'loss': loss, 
            'vocab_size': vocab_size
        }

        if tokenizer and tokenizer_vocab_path and tokenizer_merges_path:
            # Ensure tokenizer saving happens within the specific checkpoint directory
            tokenizer_save_dir = os.path.join(phase_checkpoint_dir, f"tokenizer_epoch_{epoch+1}")
            os.makedirs(tokenizer_save_dir, exist_ok=True)
            tokenizer_vocab_filename = os.path.basename(tokenizer_vocab_path)
            tokenizer_merges_filename = os.path.basename(tokenizer_merges_path)
            
            # Update paths to be relative to the tokenizer_save_dir for internal consistency
            # and store these relative paths in the checkpoint for loading.
            relative_vocab_path = os.path.join(f"tokenizer_epoch_{epoch+1}", tokenizer_vocab_filename)
            relative_merges_path = os.path.join(f"tokenizer_epoch_{epoch+1}", tokenizer_merges_filename)

            tokenizer.save(
                os.path.join(tokenizer_save_dir, tokenizer_vocab_filename),
                os.path.join(tokenizer_save_dir, tokenizer_merges_filename)
            )
            checkpoint['tokenizer_vocab_path'] = relative_vocab_path
            checkpoint['tokenizer_merges_path'] = relative_merges_path
            print(f"Tokenizer saved to {tokenizer_save_dir}")

        torch.save(checkpoint, path)
        print(f"Checkpoint saved to {path} for epoch {epoch+1}")
        return path

    def load_checkpoint(self, path: str, device: torch.device, 
                        model: torch.nn.Module = None, optimizer: torch.optim.Optimizer = None, 
                        scheduler: torch.optim.lr_scheduler._LRScheduler = None,
                        tokenizer=None, tokenizer_load_dir: Optional[str] = None):
        """
        Loads a checkpoint from the specified path.
        """
        if not os.path.exists(path):
            raise FileNotFoundError(f"Checkpoint file not found at: {path}")
        
        print(f"Loading checkpoint from {path}...")
        checkpoint = torch.load(path, map_location=device)
        
        if model:
            model.load_state_dict(checkpoint['model_state_dict'])
        if optimizer:
            optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        if scheduler:
            scheduler.load_state_dict(checkpoint['scheduler_state_dict'])
        
        if tokenizer and 'tokenizer_vocab_path' in checkpoint and 'tokenizer_merges_path' in checkpoint and tokenizer_load_dir:
            # Construct absolute paths for loading the tokenizer
            abs_vocab_path = os.path.join(os.path.dirname(path), checkpoint['tokenizer_vocab_path'])
            abs_merges_path = os.path.join(os.path.dirname(path), checkpoint['tokenizer_merges_path'])
            tokenizer.load(abs_vocab_path, abs_merges_path)
            print(f"Tokenizer loaded from {os.path.dirname(path)}")

        print(f"Checkpoint loaded successfully. Resuming from epoch {checkpoint['epoch']+1}")
        return checkpoint

if __name__ == "__main__":
    # Example usage for testing
    import torch.nn as nn
    import torch.optim as optim
    from src.training.optimizers import get_optimizer # Re-using get_optimizer for test setup
    from src.training.schedulers import get_scheduler # Re-using get_scheduler for test setup
    from src.preprocessing.improved_tokenizer import ImprovedBPETokenizer

    # Dummy Model, Optimizer, Scheduler for testing
    class DummyModel(nn.Module):
        def __init__(self):
            super().__init__()
            self.linear = nn.Linear(10, 1)

        def forward(self, x):
            return self.linear(x)

    model = DummyModel()
    optimizer = get_optimizer(model.parameters(), learning_rate=0.01)
    scheduler = get_scheduler(optimizer)

    device = torch.device("mps" if torch.backends.mps.is_available() else "cpu")
    model.to(device)

    # Initialize CheckpointManager
    checkpoint_manager = CheckpointManager(checkpoint_dir="test_checkpoints")

    # Create a dummy tokenizer for testing
    dummy_tokenizer = ImprovedBPETokenizer()
    dummy_tokenizer.train(["hello world", "bpe tokenizer test"], vocab_size=100)

    # Test saving a checkpoint with tokenizer
    test_epoch = 0
    test_loss = 5.0
    test_vocab_size = len(dummy_tokenizer.vocab)
    saved_path = checkpoint_manager.save_checkpoint(
        model, optimizer, scheduler, 
        test_epoch, test_loss, test_vocab_size,
        phase="test_phase",
        tokenizer=dummy_tokenizer, # Pass tokenizer object
        tokenizer_vocab_path=os.path.join("data/vocab", "tokenizer_model.json"), # Dummy path, actual saving handled internally
        tokenizer_merges_path=os.path.join("data/vocab", "merges.json") # Dummy path
    )
    print(f"Saved checkpoint to: {saved_path}")

    # Test loading a checkpoint with tokenizer
    new_model = DummyModel()
    new_optimizer = get_optimizer(new_model.parameters(), learning_rate=0.01)
    new_scheduler = get_scheduler(new_optimizer)
    new_tokenizer = ImprovedBPETokenizer() # Create a new tokenizer instance to load into

    loaded_checkpoint = checkpoint_manager.load_checkpoint(
        saved_path, device, 
        new_model, new_optimizer, new_scheduler,
        tokenizer=new_tokenizer, # Pass new tokenizer object for loading
        tokenizer_load_dir=os.path.dirname(saved_path) # Pass the directory where tokenizer was saved
    )
    print(f"Loaded epoch: {loaded_checkpoint['epoch']}")
    print(f"Loaded loss: {loaded_checkpoint['loss']}")
    print(f"Loaded vocab_size: {loaded_checkpoint['vocab_size']}")

    # Verify model states are the same (simple check)
    for p1, p2 in zip(model.parameters(), new_model.parameters()):
        assert torch.equal(p1, p2), "Model parameters mismatch after loading!"
    print("Model parameters match after loading.")

    # Verify tokenizer vocab sizes match (simple check)
    assert len(dummy_tokenizer.vocab) == len(new_tokenizer.vocab), "Tokenizer vocab size mismatch after loading!"
    print("Tokenizer vocab size matches after loading.")

    # Test loading non-existent checkpoint
    try:
        checkpoint_manager.load_checkpoint("non_existent_path.pt", device)
    except FileNotFoundError as e:
        print(f"Caught expected error: {e}")

    # Clean up test files
    import shutil
    shutil.rmtree("test_checkpoints")
    print("Cleaned up test_checkpoints directory.") 