import collections
import os
import json
import re
import pickle
from typing import List, Dict, Tuple

class BPETokenizer:
    """
    Improved BPE tokenizer for better English text generation.
    Addresses vocabulary size, unknown tokens, and text quality issues.
    """
    def __init__(self):
        self.vocab = {}
        self.merges = {} # Stores (pair_tuple): new_token_string (e.g., ('a', 'b'): 'ab')
        self.inverse_vocab = {}

        # Improved special tokens
        self.pad_token = '<pad>'
        self.unk_token = '<unk>'
        self.bos_token = '<bos>'  # Beginning of sequence
        self.eos_token = '<eos>'  # End of sequence
        self.special_tokens = [self.pad_token, self.unk_token, self.bos_token, self.eos_token]

    def get_pairs(self, tokens):
        """
        Get all unique adjacent pairs from a list of tokens.
        """
        pairs = collections.defaultdict(int)
        for i in range(len(tokens) - 1):
            pairs[(tokens[i], tokens[i+1])] += 1
        return pairs

    def clean_text(self, text: str) -> str:
        """Clean and normalize text for better tokenization."""
        # Remove excessive whitespace
        text = re.sub(r'\s+', ' ', text)
        # Remove control characters
        text = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]', '', text)
        # Normalize quotes
        text = text.replace('"', '"').replace('"', '"')
        text = text.replace(''', "'").replace(''', "'")
        return text.strip()

    def is_repetitive_text(self, text: str) -> bool:
        """Check if text contains repetitive patterns that should be filtered."""
        repetitive_patterns = [
            "to help language model learn patterns",
            "diverse collection",
            "model will learn",
            "patterns from this",
            "help train our"
        ]

        text_lower = text.lower()
        for pattern in repetitive_patterns:
            if pattern in text_lower:
                return True

        # Check for excessive repetition of short phrases
        words = text.split()
        if len(words) > 5:
            for i in range(len(words) - 2):
                phrase = ' '.join(words[i:i+3])
                if text.count(phrase) > 2:
                    return True
        return False

    def train(self, texts, vocab_size=8000):
        """
        Improved BPE training with better text filtering and larger vocabulary.
        """
        print(f"Training tokenizer with vocab_size={vocab_size}")

        # Clean and filter texts
        cleaned_texts = []
        for text in texts:
            cleaned = self.clean_text(text)
            # Filter out very short or repetitive texts
            if len(cleaned) > 10 and not self.is_repetitive_text(cleaned):
                cleaned_texts.append(cleaned)

        print(f"Cleaned {len(cleaned_texts)} texts from {len(texts)} original texts")

        # Initialize vocabulary with special tokens first
        self.vocab = {token: i for i, token in enumerate(self.special_tokens)}
        self.inverse_vocab = {i: token for i, token in enumerate(self.special_tokens)}
        current_id = len(self.special_tokens)

        # Build character vocabulary from cleaned texts
        all_chars = set()
        for text in cleaned_texts:
            for char in text:
                all_chars.add(char)

        # Add end-of-word marker
        all_chars.add('</w>')

        # Add characters to vocabulary
        for char in sorted(all_chars):
            if char not in self.vocab:
                self.vocab[char] = current_id
                self.inverse_vocab[current_id] = char
                current_id += 1

        self.merges = {} # Stores (pair_tuple): new_token_string (e.g., ('a', 'b'): 'ab')

        # Prepare initial word segments for training
        # Each "word" (from splitting by space) becomes a sequence of its characters + </w>
        # e.g., "hello world" -> {"h e l l o </w>": count, "w o r l d </w>": count}
        word_segments_frequency = collections.defaultdict(int)
        for text in texts:
            # Simple word split by space. Consider more robust splitting later if needed.
            for word in text.split(' '):
                if word: # Ensure not empty string from multiple spaces
                    # Represent word as space-separated characters + '</w>' for BPE processing
                    char_segment = ' '.join(list(word)) + ' </w>'
                    word_segments_frequency[char_segment] += 1
        
        # `current_segments`: a dictionary mapping space-separated token strings (representing words) to their counts.
        current_segments = dict(word_segments_frequency) # Make a copy

        while len(self.vocab) < vocab_size:
            pairs_frequency = collections.defaultdict(int)
            for segment_str, count in current_segments.items():
                tokens_in_segment = segment_str.split(' ')
                current_pairs = self.get_pairs(tokens_in_segment)
                for pair, freq in current_pairs.items():
                    pairs_frequency[pair] += freq * count # Accumulate frequency weighted by segment count

            if not pairs_frequency:
                break # No more pairs to merge

            # Find the most frequent pair
            # Fixing linter error by explicitly getting items and using lambda
            best_pair = max(pairs_frequency.items(), key=lambda item: item[1])[0] 
            
            first_token, second_token = best_pair
            new_merged_token_string = first_token + second_token

            # Store the merge rule
            self.merges[best_pair] = new_merged_token_string

            # Update `current_segments` by applying the merge
            updated_segments = collections.defaultdict(int)
            for segment_str, count in current_segments.items():
                tokens_in_segment = segment_str.split(' ')
                
                # Replace occurrences of the best_pair in the segment's tokens
                # This needs to be done carefully to avoid unintended replacements.
                # Use a string replace on the space-separated string, but ensure it doesn't break existing tokens.
                # Example: "a b c" replace "a b" with "ab" -> "ab c"
                
                # Directly rebuild the token list for robustness
                i = 0
                new_tokens_for_segment = []
                while i < len(tokens_in_segment):
                    if i < len(tokens_in_segment) - 1 and \
                       tokens_in_segment[i] == first_token and \
                       tokens_in_segment[i+1] == second_token:
                        new_tokens_for_segment.append(new_merged_token_string)
                        i += 2
                    else:
                        new_tokens_for_segment.append(tokens_in_segment[i])
                        i += 1
                updated_segments[' '.join(new_tokens_for_segment)] += count
            current_segments = updated_segments

            # Add the new merged token to the vocabulary
            if new_merged_token_string not in self.vocab:
                self.vocab[new_merged_token_string] = current_id
                self.inverse_vocab[current_id] = new_merged_token_string
                current_id += 1

        print(f"BPE tokenizer trained with vocabulary size: {len(self.vocab)}")


    def encode(self, text):
        """
        Improved encoding with better text handling and unknown token management.
        """
        if not isinstance(text, str):
            raise TypeError("Input text must be a string.")

        # Clean the text first
        text = self.clean_text(text)
        if not text.strip():
            return []

        words_in_text = text.split()
        encoded_token_ids = []

        for word in words_in_text:
            if not word.strip():
                continue

            # Prepare word for encoding: split into characters and add end-of-word token
            tokens_for_word_processing = list(word) + ['</w>']

            # Iteratively apply merges to the current list of tokens for this word
            while True:
                # Get all possible adjacent pairs in the current word's token list
                current_pairs_in_word = self.get_pairs(tokens_for_word_processing)

                potential_merges = []
                for p in current_pairs_in_word.keys():
                    if p in self.merges:
                        potential_merges.append((p, self.merges[p]))
                
                if not potential_merges:
                    break # No more mergeable pairs for this word

                # Select the best pair to merge. Heuristic: prioritize by length of merged token.
                # This helps ensure longer, more meaningful subwords are formed.
                # If lengths are equal, sort by pair elements for determinism.
                selected_pair_and_merged_token = max(potential_merges, 
                                                     key=lambda x: (len(x[1]), x[0])) # x[0] is the pair tuple itself
                
                selected_pair = selected_pair_and_merged_token[0] # The (first, second) tuple
                new_token_str = selected_pair_and_merged_token[1] # The merged string (e.g., 'he')

                first, second = selected_pair

                # Apply the merge by rebuilding the list of tokens for the current word
                j = 0
                new_tokens_for_word_processing = []
                while j < len(tokens_for_word_processing):
                    if j < len(tokens_for_word_processing) - 1 and \
                       tokens_for_word_processing[j] == first and \
                       tokens_for_word_processing[j+1] == second:
                        new_tokens_for_word_processing.append(new_token_str)
                        j += 2
                    else:
                        new_tokens_for_word_processing.append(tokens_for_word_processing[j])
                        j += 1
                tokens_for_word_processing = new_tokens_for_word_processing # Update the tokens for the current word

            # After all merges for a word are applied, convert to token IDs
            for token in tokens_for_word_processing:
                if token in self.vocab:
                    encoded_token_ids.append(self.vocab[token])
                else:
                    # Fallback for OOV (Out-Of-Vocabulary) tokens or characters not in initial vocab.
                    # Assign UNK token ID for any token not found in the vocabulary.
                    encoded_token_ids.append(self.vocab[self.unk_token])
            


        return encoded_token_ids

    def decode(self, token_ids):
        """
        Improved decoding with better text reconstruction and special token handling.
        """
        if not token_ids:
            return ""

        decoded_parts = []
        for token_id in token_ids:
            if token_id in self.inverse_vocab:
                token = self.inverse_vocab[token_id]
                # Skip special tokens except end-of-word
                if token not in [self.pad_token, self.bos_token, self.eos_token]:
                    decoded_parts.append(token)

        # Join all decoded parts
        raw_decoded_string = "".join(decoded_parts)

        # Replace end-of-word markers with spaces
        text_with_spaces = raw_decoded_string.replace('</w>', ' ')

        # Clean up extra spaces
        final_text = re.sub(r'\s+', ' ', text_with_spaces).strip()

        return final_text

    def save_tokenizer(self, vocab_path="data/vocab/tokenizer_model.json", merges_path="data/vocab/merges.json"):
        """
        Saves the tokenizer vocabulary and merges to JSON files.
        """
        os.makedirs(os.path.dirname(vocab_path), exist_ok=True)
        
        with open(vocab_path, "w", encoding="utf-8") as f:
            json.dump(self.vocab, f, ensure_ascii=False, indent=4)
        print(f"Tokenizer vocabulary saved to {vocab_path}")

        # Save merges: Convert tuple keys to strings for JSON serialization
        serializable_merges = {f"{p[0]} {p[1]}": merged_token for p, merged_token in self.merges.items()}
        with open(merges_path, "w", encoding="utf-8") as f:
            json.dump(serializable_merges, f, ensure_ascii=False, indent=4)
        print(f"Tokenizer merges saved to {merges_path}")

    @classmethod
    def load_tokenizer(cls, vocab_path="data/vocab/tokenizer_model.json", merges_path="data/vocab/merges.json"):
        """
        Loads the tokenizer vocabulary and merges from JSON files.
        """
        tokenizer = cls()
        with open(vocab_path, "r", encoding="utf-8") as f:
            tokenizer.vocab = json.load(f)
        
        # Reconstruct inverse_vocab
        tokenizer.inverse_vocab = {v: k for k, v in tokenizer.vocab.items()}

        try:
            with open(merges_path, "r", encoding="utf-8") as f:
                serializable_merges = json.load(f)
                # Convert string keys back to tuple keys
                tokenizer.merges = {tuple(k.split(' ')): v for k, v in serializable_merges.items()}
        except FileNotFoundError:
            print(f"Warning: Merges file not found at {merges_path}. Tokenizer will operate without merges.")
            tokenizer.merges = {}
        except json.JSONDecodeError:
            print(f"Warning: Error decoding merges from {merges_path}. Tokenizer will operate without merges.")
            tokenizer.merges = {}
        
        print(f"Tokenizer loaded from {vocab_path} and {merges_path}")
        return tokenizer

    def save(self, filepath):
        """Save tokenizer to file using pickle for better compatibility."""
        tokenizer_data = {
            'vocab': self.vocab,
            'merges': self.merges,
            'inverse_vocab': self.inverse_vocab,
            'special_tokens': self.special_tokens
        }

        with open(filepath, 'wb') as f:
            pickle.dump(tokenizer_data, f)
        print(f"Tokenizer saved to {filepath}")

    def load(self, filepath):
        """Load tokenizer from file."""
        with open(filepath, 'rb') as f:
            tokenizer_data = pickle.load(f)

        self.vocab = tokenizer_data['vocab']
        self.merges = tokenizer_data['merges']
        self.inverse_vocab = tokenizer_data['inverse_vocab']
        self.special_tokens = tokenizer_data.get('special_tokens', [self.pad_token, self.unk_token])
        print(f"Tokenizer loaded from {filepath}")

# Example usage (for testing purposes, not part of the main workflow)
if __name__ == "__main__":
    texts = [
        "hello world",
        "world peace",
        "apple pie",
        "apples are delicious",
        "apple",
        "apples",
        "This is a test sentence.", # Added a more complex sentence
        "programming language",
        "data science"
    ]
    
    tokenizer = BPETokenizer()
    # Increased vocab_size for more merges
    tokenizer.train(texts, vocab_size=300) 

    test_text_1 = "hello apple delicious peace"
    encoded_1 = tokenizer.encode(test_text_1)
    print(f"Encoded '{test_text_1}': {encoded_1}")
    print(f"Decoded '{test_text_1}': {tokenizer.decode(encoded_1)}")

    test_text_2 = "This is a programming language for data science."
    encoded_2 = tokenizer.encode(test_text_2)
    print(f"Encoded '{test_text_2}': {encoded_2}")
    print(f"Decoded '{test_text_2}': {tokenizer.decode(encoded_2)}")

    tokenizer.save_tokenizer()
    loaded_tokenizer = BPETokenizer.load_tokenizer()
    encoded_loaded_1 = loaded_tokenizer.encode(test_text_1)
    print(f"Encoded (loaded) '{test_text_1}': {encoded_loaded_1}")
    print(f"Decoded (loaded) '{test_text_1}': {loaded_tokenizer.decode(encoded_loaded_1)}")
    
    encoded_loaded_2 = loaded_tokenizer.encode(test_text_2)
    print(f"Encoded (loaded) '{test_text_2}': {encoded_loaded_2}")
    print(f"Decoded (loaded) '{test_text_2}': {loaded_tokenizer.decode(encoded_loaded_2)}")

    # Test with OOV character handling
    oov_text_char = "new£word"
    encoded_oov_char = tokenizer.encode(oov_text_char)
    print(f"Encoded '{oov_text_char}': {encoded_oov_char}")
    print(f"Decoded '{oov_text_char}': {tokenizer.decode(encoded_oov_char)}")

    # Test with OOV word handling
    oov_word_text = "This is an unknownword for testing."
    encoded_oov_word = tokenizer.encode(oov_word_text)
    print(f"Encoded '{oov_word_text}': {encoded_oov_word}")
    print(f"Decoded '{oov_word_text}': {tokenizer.decode(encoded_oov_word)}")

    # Test with invalid input
    try:
        tokenizer.encode(123)
    except TypeError as e:
        print(f"Caught expected error: {e}")

    # Test loading non-existent tokenizer
    print("\nTesting loading non-existent tokenizer:")
    loaded_tokenizer_fail = BPETokenizer.load_tokenizer(vocab_path="non_existent_vocab.json", merges_path="non_existent_merges.json")
    encoded_fail = loaded_tokenizer_fail.encode("hello world")
    print(f"Encoded with failed load: {encoded_fail}") 