import re
import string
from typing import List, Set
from collections import Counter

class TextDataCleaner:
    """
    Advanced text cleaning and filtering for language model training.
    Removes repetitive patterns, metadata, and low-quality text.
    """
    
    def __init__(self):
        # Patterns that indicate metadata or repetitive training text
        self.bad_patterns = [
            r"to help.*language model.*learn",
            r"model will learn patterns",
            r"diverse collection",
            r"help train our",
            r"patterns from this",
            r"language model.*patterns",
            r"collection of.*text",
            r"training.*data.*model",
            r"learn.*patterns.*from",
            r"this.*diverse.*collection"
        ]
        
        # Compile patterns for efficiency
        self.compiled_patterns = [re.compile(pattern, re.IGNORECASE) for pattern in self.bad_patterns]
        
        # Common Wikipedia/dataset metadata patterns
        self.metadata_patterns = [
            r"^=+.*=+$",  # Wikipedia headers
            r"^\s*\|.*\|.*$",  # Table rows
            r"^\s*\{\{.*\}\}.*$",  # Template syntax
            r"^\s*\[\[.*\]\].*$",  # Wiki links
            r"^Category:",
            r"^File:",
            r"^Template:",
            r"^User:",
            r"^\s*\*\s*$",  # Empty bullet points
            r"^\s*#\s*$",  # Empty numbered lists
        ]
        
        self.compiled_metadata = [re.compile(pattern, re.MULTILINE) for pattern in self.metadata_patterns]
    
    def is_repetitive_text(self, text: str) -> bool:
        """Check if text contains repetitive patterns that hurt training."""
        # Check against known bad patterns
        for pattern in self.compiled_patterns:
            if pattern.search(text):
                return True
        
        # Check for excessive repetition of phrases
        words = text.split()
        if len(words) < 10:
            return False
        
        # Check for repeated 3-word phrases
        phrase_counts = Counter()
        for i in range(len(words) - 2):
            phrase = ' '.join(words[i:i+3]).lower()
            phrase_counts[phrase] += 1
        
        # If any 3-word phrase appears more than twice, it's likely repetitive
        for count in phrase_counts.values():
            if count > 2:
                return True
        
        # Check for repeated sentences
        sentences = re.split(r'[.!?]+', text)
        sentence_counts = Counter(s.strip().lower() for s in sentences if s.strip())
        for count in sentence_counts.values():
            if count > 1:
                return True
        
        return False
    
    def has_metadata(self, text: str) -> bool:
        """Check if text contains Wikipedia or dataset metadata."""
        for pattern in self.compiled_metadata:
            if pattern.search(text):
                return True
        return False
    
    def is_high_quality(self, text: str) -> bool:
        """Check if text meets quality standards for training."""
        # Basic length check
        if len(text.strip()) < 50:
            return False
        
        # Check word count
        words = text.split()
        if len(words) < 10:
            return False
        
        # Check for reasonable sentence structure
        sentences = re.split(r'[.!?]+', text)
        valid_sentences = [s for s in sentences if len(s.strip().split()) >= 3]
        if len(valid_sentences) < 2:
            return False
        
        # Check character diversity (avoid texts with too many repeated characters)
        char_counts = Counter(text.lower())
        total_chars = len(text)
        
        # If any single character makes up more than 20% of the text, it's likely low quality
        for char, count in char_counts.items():
            if char.isalnum() and count / total_chars > 0.2:
                return False
        
        # Check for reasonable punctuation ratio
        punct_count = sum(1 for c in text if c in string.punctuation)
        if punct_count / len(text) > 0.3:  # Too much punctuation
            return False
        
        # Check for reasonable capitalization
        alpha_chars = [c for c in text if c.isalpha()]
        if alpha_chars:
            upper_ratio = sum(1 for c in alpha_chars if c.isupper()) / len(alpha_chars)
            if upper_ratio > 0.5:  # Too many capitals
                return False
        
        return True
    
    def clean_text(self, text: str) -> str:
        """Clean individual text sample."""
        # Remove excessive whitespace
        text = re.sub(r'\s+', ' ', text)
        
        # Remove control characters
        text = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]', '', text)
        
        # Normalize quotes and apostrophes
        text = text.replace('"', '"').replace('"', '"')
        text = text.replace(''', "'").replace(''', "'")
        
        # Remove excessive punctuation
        text = re.sub(r'[.]{3,}', '...', text)
        text = re.sub(r'[!]{2,}', '!', text)
        text = re.sub(r'[?]{2,}', '?', text)
        
        # Remove URLs
        text = re.sub(r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+', '', text)
        
        # Remove email addresses
        text = re.sub(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b', '', text)
        
        # Clean up remaining whitespace
        text = re.sub(r'\s+', ' ', text).strip()
        
        return text
    
    def filter_texts(self, texts: List[str], max_texts: int = None) -> List[str]:
        """Filter and clean a list of texts for training."""
        print(f"Starting with {len(texts)} texts")
        
        cleaned_texts = []
        filtered_counts = {
            'too_short': 0,
            'repetitive': 0,
            'metadata': 0,
            'low_quality': 0,
            'kept': 0
        }
        
        for i, text in enumerate(texts):
            if i % 1000 == 0:
                print(f"Processed {i}/{len(texts)} texts", end='\r')
            
            # Clean the text first
            cleaned = self.clean_text(text)
            
            # Apply filters
            if len(cleaned.strip()) < 50:
                filtered_counts['too_short'] += 1
                continue
            
            if self.is_repetitive_text(cleaned):
                filtered_counts['repetitive'] += 1
                continue
            
            if self.has_metadata(cleaned):
                filtered_counts['metadata'] += 1
                continue
            
            if not self.is_high_quality(cleaned):
                filtered_counts['low_quality'] += 1
                continue
            
            cleaned_texts.append(cleaned)
            filtered_counts['kept'] += 1
            
            # Stop if we have enough texts
            if max_texts and len(cleaned_texts) >= max_texts:
                break
        
        print(f"\nFiltering complete!")
        print(f"Original texts: {len(texts)}")
        print(f"Kept: {filtered_counts['kept']}")
        print(f"Filtered out:")
        print(f"  - Too short: {filtered_counts['too_short']}")
        print(f"  - Repetitive: {filtered_counts['repetitive']}")
        print(f"  - Metadata: {filtered_counts['metadata']}")
        print(f"  - Low quality: {filtered_counts['low_quality']}")
        
        return cleaned_texts
    
    def get_text_stats(self, texts: List[str]) -> dict:
        """Get statistics about the text collection."""
        if not texts:
            return {}
        
        total_chars = sum(len(text) for text in texts)
        total_words = sum(len(text.split()) for text in texts)
        
        lengths = [len(text) for text in texts]
        word_counts = [len(text.split()) for text in texts]
        
        stats = {
            'num_texts': len(texts),
            'total_characters': total_chars,
            'total_words': total_words,
            'avg_chars_per_text': total_chars / len(texts),
            'avg_words_per_text': total_words / len(texts),
            'min_chars': min(lengths),
            'max_chars': max(lengths),
            'min_words': min(word_counts),
            'max_words': max(word_counts)
        }
        
        return stats

def test_cleaner():
    """Test the text cleaner with sample data."""
    cleaner = TextDataCleaner()
    
    # Test texts with various issues
    test_texts = [
        "This is a good quality text with proper sentences. It has multiple sentences and good structure.",
        "to help language model learn patterns from this diverse collection",  # Should be filtered
        "= Wikipedia Header =",  # Should be filtered
        "Short text",  # Should be filtered (too short)
        "This text repeats the same phrase. This text repeats the same phrase. This text repeats the same phrase.",  # Should be filtered
        "A well-written paragraph about artificial intelligence and its applications in modern society. The technology continues to evolve rapidly.",
        "ALLCAPSTEXT WITH TOO MANY CAPITALS AND POOR QUALITY!!!!!!",  # Should be filtered
        "Normal text with good quality and proper sentence structure. It discusses various topics in a coherent manner."
    ]
    
    print("Testing text cleaner...")
    filtered = cleaner.filter_texts(test_texts)
    
    print(f"\nResults:")
    for i, text in enumerate(filtered):
        print(f"{i+1}. {text[:100]}...")
    
    stats = cleaner.get_text_stats(filtered)
    print(f"\nStats: {stats}")

if __name__ == "__main__":
    test_cleaner()
