import torch
import torch.nn as nn
import math

class TokenEmbedding(nn.<PERSON><PERSON><PERSON>):
    """
    Converts token IDs into dense vectors.
    """
    def __init__(self, vocab_size: int, embed_dim: int):
        super().__init__()
        self.embedding = nn.Embedding(vocab_size, embed_dim)
        self.embed_dim = embed_dim

    def forward(self, tokens: torch.Tensor) -> torch.Tensor:
        # Scale the embeddings by sqrt(embed_dim) as per the Transformer paper
        return self.embedding(tokens) * math.sqrt(self.embed_dim)

class PositionalEncoding(nn.Module):
    """
    Implements the sinusoidal positional encoding from the Transformer paper.
    """
    def __init__(self, embed_dim: int, max_seq_len: int = 512, dropout: float = 0.1):
        super().__init__()
        self.dropout = nn.Dropout(p=dropout)

        position = torch.arange(max_seq_len).unsqueeze(1)
        div_term = torch.exp(torch.arange(0, embed_dim, 2) * -(math.log(10000.0) / embed_dim))
        pe = torch.zeros(max_seq_len, embed_dim)
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        pe = pe.unsqueeze(0) # Add batch dimension
        self.register_buffer('pe', pe)

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        Args:
            x: Tensor, shape (batch_size, seq_len, embed_dim)
        """
        # Add positional encoding to the token embeddings
        # Slicing pe to match the sequence length of the input tensor x
        x = x + self.pe[:, :x.size(1)]
        return self.dropout(x)

# Example usage (for testing purposes)
if __name__ == "__main__":
    # Test TokenEmbedding
    vocab_size = 10000
    embed_dim = 512
    token_embedding = TokenEmbedding(vocab_size, embed_dim)
    
    # Simulate a batch of token IDs
    input_tokens = torch.randint(0, vocab_size, (4, 10)) # Batch size 4, sequence length 10
    embedded_tokens = token_embedding(input_tokens)
    print(f"TokenEmbedding output shape: {embedded_tokens.shape}") # Expected: (4, 10, 512)

    # Test PositionalEncoding
    max_seq_len = 100
    positional_encoding = PositionalEncoding(embed_dim, max_seq_len)
    
    # Apply positional encoding to embedded tokens
    # Ensure sequence length of embedded_tokens does not exceed max_seq_len of PE
    pos_encoded_output = positional_encoding(embedded_tokens)
    print(f"PositionalEncoding output shape: {pos_encoded_output.shape}") # Expected: (4, 10, 512)

    # Verify that positional encoding adds to values (not just shape)
    print(f"Original embedding sample: {embedded_tokens[0, 0, :5]}")
    print(f"Positional encoded sample: {pos_encoded_output[0, 0, :5]}")
    assert not torch.equal(embedded_tokens, pos_encoded_output), "Positional encoding did not modify embeddings!" 