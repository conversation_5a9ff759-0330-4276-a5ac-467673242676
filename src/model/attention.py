import torch
import torch.nn as nn
import torch.nn.functional as F
import math

class MultiHeadSelfAttention(nn.Module):
    """
    Implements Multi-Head Self-Attention as described in the "Attention Is All You Need" paper.
    """
    def __init__(self, embed_dim: int, num_heads: int, dropout: float = 0.1):
        super().__init__()
        if embed_dim % num_heads != 0:
            raise ValueError("embed_dim must be divisible by num_heads")
        
        self.embed_dim = embed_dim
        self.num_heads = num_heads
        self.head_dim = embed_dim // num_heads

        self.query_projection = nn.Linear(embed_dim, embed_dim)
        self.key_projection = nn.Linear(embed_dim, embed_dim)
        self.value_projection = nn.Linear(embed_dim, embed_dim)

        self.fc_out = nn.Linear(embed_dim, embed_dim)
        self.dropout = nn.Dropout(dropout)

    def forward(self, query: torch.Tensor, key: torch.Tensor, value: torch.Tensor, mask: torch.Tensor = None) -> torch.Tensor:
        """
        Args:
            query, key, value: Tensors, shape (batch_size, seq_len, embed_dim)
            mask: Optional tensor, shape (batch_size, 1, 1, seq_len) or (1, 1, seq_len, seq_len)
                  (for causal masking or padding masking). Contains 0 for positions to mask, 1 otherwise.
        """
        batch_size = query.shape[0]
        
        # 1. Linear projections and split into heads
        # (batch_size, seq_len, embed_dim) -> (batch_size, seq_len, num_heads, head_dim) -> (batch_size, num_heads, seq_len, head_dim)
        query = self.query_projection(query).view(batch_size, -1, self.num_heads, self.head_dim).transpose(1, 2)
        key = self.key_projection(key).view(batch_size, -1, self.num_heads, self.head_dim).transpose(1, 2)
        value = self.value_projection(value).view(batch_size, -1, self.num_heads, self.head_dim).transpose(1, 2)

        # 2. Scaled Dot-Product Attention (Q @ K_T / sqrt(d_k))
        # (batch_size, num_heads, seq_len, head_dim) @ (batch_size, num_heads, head_dim, seq_len) -> (batch_size, num_heads, seq_len, seq_len)
        energy = torch.matmul(query, key.transpose(-2, -1)) / math.sqrt(self.head_dim)

        # Apply mask if provided
        if mask is not None:
            energy = energy.masked_fill(mask == 0, float('-1e4')) # Large negative value for masked positions (fixed for mixed precision)

        attention_scores = F.softmax(energy, dim=-1)
        attention_scores = self.dropout(attention_scores)

        # 3. Multiply by Value
        # (batch_size, num_heads, seq_len, seq_len) @ (batch_size, num_heads, seq_len, head_dim) -> (batch_size, num_heads, seq_len, head_dim)
        output = torch.matmul(attention_scores, value)

        # 4. Concatenate heads and final linear projection
        # (batch_size, num_heads, seq_len, head_dim) -> (batch_size, seq_len, num_heads, head_dim) -> (batch_size, seq_len, embed_dim)
        output = output.transpose(1, 2).contiguous().view(batch_size, -1, self.embed_dim)
        output = self.fc_out(output)
        return output

# Example usage (for testing purposes)
if __name__ == "__main__":
    embed_dim = 512
    num_heads = 8
    batch_size = 2
    seq_len = 10

    # Simulate input tensor (e.g., from embeddings layer)
    input_tensor = torch.randn(batch_size, seq_len, embed_dim)

    # Create a MultiHeadSelfAttention instance
    attention_module = MultiHeadSelfAttention(embed_dim, num_heads)

    # Test forward pass without mask
    output_no_mask = attention_module(input_tensor, input_tensor, input_tensor)
    print(f"Output shape (no mask): {output_no_mask.shape}") # Expected: (2, 10, 512)

    # Test forward pass with causal mask
    # Causal mask: for each position i, it can only attend to positions j <= i.
    causal_mask = torch.triu(torch.ones(seq_len, seq_len), diagonal=1).type(torch.bool).unsqueeze(0).unsqueeze(0)
    # Invert the mask for masked_fill (0 means mask, 1 means keep)
    causal_mask = ~causal_mask # Now 0s are upper triangle, 1s are lower triangle (including diagonal)
    
    output_with_mask = attention_module(input_tensor, input_tensor, input_tensor, mask=causal_mask)
    print(f"Output shape (with mask): {output_with_mask.shape}") # Expected: (2, 10, 512)

    # Test value error for invalid embed_dim and num_heads
    try:
        MultiHeadSelfAttention(embed_dim=513, num_heads=8)
    except ValueError as e:
        print(f"Caught expected error: {e}")

    print("MultiHeadSelfAttention tests passed.") 