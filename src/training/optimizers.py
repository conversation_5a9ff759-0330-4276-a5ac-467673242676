import torch.optim as optim
from torch.optim.optimizer import Optimizer

def get_optimizer(model_parameters, learning_rate: float, optimizer_type: str = "AdamW") -> Optimizer:
    """
    Returns an optimizer for the given model parameters.

    Args:
        model_parameters: The parameters of the model to optimize.
        learning_rate: The learning rate for the optimizer.
        optimizer_type: The type of optimizer (e.g., "AdamW", "Adam", "SGD").

    Returns:
        A PyTorch optimizer instance.
    """
    if optimizer_type == "AdamW":
        optimizer = optim.AdamW(model_parameters, lr=learning_rate)
    elif optimizer_type == "Adam":
        optimizer = optim.Adam(model_parameters, lr=learning_rate)
    elif optimizer_type == "SGD":
        optimizer = optim.SGD(model_parameters, lr=learning_rate)
    else:
        raise ValueError(f"Unsupported optimizer type: {optimizer_type}")
    
    return optimizer

if __name__ == "__main__":
    # Example usage:
    import torch.nn as nn

    class DummyModel(nn.Module):
        def __init__(self):
            super().__init__()
            self.linear = nn.Linear(10, 1)

        def forward(self, x):
            return self.linear(x)

    model = DummyModel()
    learning_rate = 0.001

    # Test AdamW
    optimizer_adamw = get_optimizer(model.parameters(), learning_rate, "AdamW")
    print(f"AdamW Optimizer: {optimizer_adamw}")

    # Test Adam
    optimizer_adam = get_optimizer(model.parameters(), learning_rate, "Adam")
    print(f"Adam Optimizer: {optimizer_adam}")

    # Test SGD
    optimizer_sgd = get_optimizer(model.parameters(), learning_rate, "SGD")
    print(f"SGD Optimizer: {optimizer_sgd}")

    # Test unsupported optimizer
    try:
        get_optimizer(model.parameters(), learning_rate, "UnsupportedOptimizer")
    except ValueError as e:
        print(f"Caught expected error: {e}")

    print("Optimizers tests passed.") 