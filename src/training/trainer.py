import torch
import torch.nn as nn
from torch.utils.data import DataLoader
import os
import json
import time

from src.model.transformer import Transformer
from src.preprocessing.improved_tokenizer import ImprovedBPETokenizer
from src.preprocessing.data_loaders import TextDataset, collate_batch
from src.training.optimizers import get_optimizer
from src.training.schedulers import get_scheduler
from src.config.model_config import ModelConfig, TrainingConfig, DataConfig
from src.utils.logging import SimpleLogger # Import the SimpleLogger
from src.utils.checkpoint_manager import CheckpointManager # Import the CheckpointManager
from src.evaluation.metrics import calculate_accuracy # Import accuracy metric
from src.evaluation.perplexity import calculate_perplexity # Import perplexity metric

class Trainer:
    def __init__(self, model: Transformer, tokenizer: ImprovedBPETokenizer, 
                 training_config: TrainingConfig, data_config: DataConfig,
                 model_config: ModelConfig,
                 device: torch.device,
                 logger: SimpleLogger):
        
        self.model = model.to(device)
        self.tokenizer = tokenizer
        self.training_config = training_config
        self.data_config = data_config
        self.model_config = model_config
        self.device = device
        self.logger = logger

        self.optimizer = get_optimizer(
            self.model.parameters(),
            self.training_config.learning_rate
        )

        # Calculate total training steps for scheduler
        # This is an approximation - will be updated when we know the actual dataset size
        estimated_steps_per_epoch = 1000  # This should be calculated from actual dataset size
        total_steps = estimated_steps_per_epoch * self.training_config.num_epochs
        warmup_steps = max(1, total_steps // 10)  # 10% warmup

        # Store these for potential updates when we know the actual dataset size
        self.estimated_total_steps = total_steps
        self.warmup_steps = warmup_steps

        self.scheduler = get_scheduler(
            self.optimizer,
            "CosineAnnealingLRWithWarmup",
            warmup_steps=warmup_steps,
            max_steps=total_steps,
            eta_min=self.training_config.learning_rate * 0.01  # 1% of initial LR as minimum
        )
        
        # Get pad_token_id directly from the tokenizer instance
        self.pad_token_id = self.tokenizer.vocab[self.tokenizer.pad_token]
        self.criterion = nn.CrossEntropyLoss(ignore_index=self.pad_token_id)

        # Initialize CheckpointManager
        self.checkpoint_manager = CheckpointManager(checkpoint_dir="checkpoints")

    def update_scheduler_for_dataset_size(self, dataset_size: int):
        """
        Update the scheduler with accurate step calculations based on actual dataset size.
        Call this after loading the dataset but before training.
        """
        steps_per_epoch = max(1, dataset_size // (self.training_config.batch_size * self.training_config.gradient_accumulation_steps))
        total_steps = steps_per_epoch * self.training_config.num_epochs
        warmup_steps = max(1, total_steps // 10)  # 10% warmup

        # Create new scheduler with accurate step counts
        self.scheduler = get_scheduler(
            self.optimizer,
            "CosineAnnealingLRWithWarmup",
            warmup_steps=warmup_steps,
            max_steps=total_steps,
            eta_min=self.training_config.learning_rate * 0.01
        )

        self.logger.log(f"Updated scheduler: {steps_per_epoch} steps/epoch, {total_steps} total steps, {warmup_steps} warmup steps")

        # Initialize gradient scaler for mixed precision training if MPS is available
        self.scaler = None
        if self.device.type == 'mps':
            self.logger.log("MPS device detected. Mixed precision training managed by PyTorch.")
        elif self.device.type == 'cuda':
            self.scaler = torch.cuda.amp.GradScaler()
            self.logger.log("CUDA device detected. Initializing GradScaler for mixed precision training.")
        else:
            self.logger.log(f"Device {self.device.type} detected. No mixed precision scaler initialized.")


    def create_masks(self, src_seq_len: int, tgt_seq_len: int) -> tuple[torch.Tensor, torch.Tensor]:
        src_mask = None

        tgt_mask = torch.triu(torch.ones(tgt_seq_len, tgt_seq_len), diagonal=1).bool().to(self.device)
        tgt_mask = ~tgt_mask  
        
        return src_mask, tgt_mask.unsqueeze(0).unsqueeze(0)


    def train_epoch(self, dataloader: DataLoader, epoch: int) -> float:
        self.model.train()
        total_loss = 0
        total_accuracy = 0
        num_active_elements = 0
        start_time = time.time()

        for batch_idx, (inputs, targets) in enumerate(dataloader):
            inputs, targets = inputs.to(self.device), targets.to(self.device)
            
            _, tgt_mask = self.create_masks(inputs.size(1), inputs.size(1))

            if (batch_idx % self.training_config.gradient_accumulation_steps == 0):
                self.optimizer.zero_grad()

            with torch.autocast(device_type=self.device.type, dtype=torch.float16 if self.device.type == 'cuda' else torch.float32):
                outputs = self.model(inputs, inputs, src_mask=None, tgt_mask=tgt_mask)
                loss = self.criterion(outputs.view(-1, outputs.size(-1)), targets.view(-1))

            if self.scaler is not None:
                self.scaler.scale(loss).backward()
                if (batch_idx + 1) % self.training_config.gradient_accumulation_steps == 0:
                    self.scaler.step(self.optimizer)
                    self.scaler.update()
                    self.scheduler.step()  # Step scheduler after optimizer step
            else:
                loss.backward()
                if (batch_idx + 1) % self.training_config.gradient_accumulation_steps == 0:
                    self.optimizer.step()
                    self.scheduler.step()  # Step scheduler after optimizer step
            
            total_loss += loss.item()

            # Calculate accuracy for logging
            batch_accuracy = calculate_accuracy(outputs, targets, ignore_index=self.pad_token_id)
            active_elements_in_batch = (targets != self.pad_token_id).sum().item()
            total_accuracy += batch_accuracy * active_elements_in_batch
            num_active_elements += active_elements_in_batch

            if (batch_idx + 1) % self.training_config.log_interval == 0:
                elapsed_time = time.time() - start_time
                avg_loss = total_loss / (batch_idx + 1)
                avg_accuracy = (total_accuracy / num_active_elements) if num_active_elements > 0 else 0.0
                avg_perplexity = calculate_perplexity(avg_loss) # Perplexity is based on average loss

                self.logger.log(
                    f"Epoch: {epoch+1}/{self.training_config.num_epochs}, "
                    f"Batch: {batch_idx+1}/{len(dataloader)}, "
                    f"Loss: {avg_loss:.4f}, "
                    f"Accuracy: {avg_accuracy:.4f}, "
                    f"Perplexity: {avg_perplexity:.4f}, "
                    f"LR: {self.optimizer.param_groups[0]['lr']:.6f}, "
                    f"Time: {elapsed_time:.2f}s"
                )

        avg_epoch_loss = total_loss / len(dataloader)
        avg_epoch_accuracy = (total_accuracy / num_active_elements) if num_active_elements > 0 else 0.0
        avg_epoch_perplexity = calculate_perplexity(avg_epoch_loss)
        self.logger.log(f"Epoch {epoch+1} finished. Average Loss: {avg_epoch_loss:.4f}, Average Accuracy: {avg_epoch_accuracy:.4f}, Average Perplexity: {avg_epoch_perplexity:.4f}")
        return avg_epoch_loss

    def train(self, train_dataloader: DataLoader):
        self.logger.log("Starting training...")
        for epoch in range(self.training_config.num_epochs):
            avg_epoch_loss = self.train_epoch(train_dataloader, epoch)
            if (epoch + 1) % self.training_config.checkpoint_interval == 0 or epoch == self.training_config.num_epochs - 1:
                self.checkpoint_manager.save_checkpoint(self.model, self.optimizer, self.scheduler, 
                                                        epoch, avg_epoch_loss, 
                                                        len(self.tokenizer.vocab), phase="phase1_english", 
                                                        tokenizer_vocab_path=os.path.join(self.data_config.vocab_dir, "tokenizer_model.json"), 
                                                        tokenizer_merges_path=os.path.join(self.data_config.vocab_dir, "merges.json"))
        self.logger.log("Training completed.")

    def load_checkpoint(self, path: str) -> int:
        checkpoint = self.checkpoint_manager.load_checkpoint(path, self.device, 
                                                              self.model, self.optimizer, self.scheduler)
        return checkpoint['epoch'] + 1


if __name__ == "__main__":
    # --- Setup for demonstration ---

    # Load configs - using improved defaults for better performance
    model_config = ModelConfig()  # Use improved defaults: embed_dim=512, num_layers=4, num_heads=8
    training_config = TrainingConfig(
        batch_size=8,  # Keep smaller for testing, but use gradient accumulation
        learning_rate=1e-4,
        num_epochs=10,  # Increased from 3 for better training
        log_interval=10,
        checkpoint_interval=2,  # Save checkpoints more frequently for testing
        gradient_accumulation_steps=4  # Compensate for smaller batch size
    )
    data_config = DataConfig(
        processed_data_dir="data/processed/english_tokens",
        vocab_dir="data/vocab"
    )

    # Determine device
    device = torch.device("mps" if torch.backends.mps.is_available() else "cuda" if torch.cuda.is_available() else "cpu")
    print(f"Using device: {device}")

    # Initialize logger
    logger = SimpleLogger()

    # Load tokenizer
    try:
        tokenizer = ImprovedBPETokenizer()
        # Load from JSON files
        vocab_path = os.path.join(data_config.vocab_dir, "tokenizer_model.json")
        merges_path = os.path.join(data_config.vocab_dir, "merges.json")
        tokenizer.load_from_json(vocab_path, merges_path)
        model_config.vocab_size = len(tokenizer.vocab) 
    except FileNotFoundError:
        logger.log("Tokenizer files not found. Please run scripts/preprocess_data.py first.")
        exit()

    # Create dataset and dataloader
    try:
        tokenized_file_path = os.path.join(data_config.processed_data_dir, "tokenized_english_corpus.json")
        dataset = TextDataset(tokenized_file_path)
        
        train_dataloader = DataLoader(dataset, 
                                      batch_size=training_config.batch_size, 
                                      shuffle=True, 
                                      collate_fn=lambda b: collate_batch(b, tokenizer.vocab[tokenizer.pad_token], model_config.max_seq_len))
    except (FileNotFoundError, ValueError) as e:
        logger.log(f"Error loading dataset: {e}")
        logger.log("Please ensure `scripts/preprocess_data.py` has been run successfully.")
        exit()
    
    # Initialize model
    model = Transformer(
        vocab_size=model_config.vocab_size,
        embed_dim=model_config.embed_dim,
        num_layers=model_config.num_layers,
        num_heads=model_config.num_heads,
        ff_dim=model_config.ff_dim,
        max_seq_len=model_config.max_seq_len,
        dropout=model_config.dropout
    )

    # Initialize Trainer
    trainer = Trainer(model, tokenizer, training_config, data_config, model_config, device, logger)

    # Update scheduler with accurate dataset size
    trainer.update_scheduler_for_dataset_size(len(dataset))

    # Start training
    trainer.train(train_dataloader)

    # Example of loading a checkpoint and continuing training or inference (optional)
    print("\nTesting checkpoint loading...")
    # Assuming epoch 1 checkpoint exists for testing
    checkpoint_path = os.path.join("checkpoints", "phase1_english", "model_epoch_1.pt")
    if os.path.exists(checkpoint_path):
        loaded_epoch = trainer.load_checkpoint(checkpoint_path)
        print(f"Loaded model state from epoch {loaded_epoch-1}. Ready to continue training from epoch {loaded_epoch}.")
    else:
        print(f"Checkpoint not found at {checkpoint_path}. Skipping load test.") 