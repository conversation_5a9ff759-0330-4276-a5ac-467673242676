# Product Overview

This is an AI language model built from scratch using a transformer-based architecture. The project follows a phased training approach:

**Phase 1: English Language Comprehension**
- Master foundational English understanding and generation
- Tasks: Masked Language Modeling (MLM), Next Sentence Prediction (NSP), Causal Language Modeling (CLM)

**Phase 2: Code Understanding & Generation** 
- Extend model to comprehend and generate programming code
- Tasks: Code completion, fill-in-the-middle, docstring generation, code summarization

**Phase 3: Advanced Reasoning & Multi-modal**
- Enable complex reasoning and integrate multiple data modalities (text + images)
- Tasks: Visual Question Answering (VQA), image captioning, logical deductions

## Key Goals
- Custom transformer implementation without high-level external APIs
- Optimized for local development on MacBook M3 with MPS acceleration
- Scalable training using Google Colab for GPU-intensive phases
- Modular, maintainable codebase for research and experimentation

## Current Status
The project is currently in Phase 1 with basic transformer architecture, BPE tokenization, and training pipeline implemented.