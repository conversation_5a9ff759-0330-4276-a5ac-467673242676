# Project Structure

## Directory Organization

### `/src/` - Source Code
**Core implementation with clear separation of concerns:**

- `config/` - Configuration classes
  - `model_config.py` - ModelConfig, TrainingConfig, DataConfig classes
- `model/` - Transformer architecture components
  - `transformer.py` - Main Transformer, Encoder<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
  - `attention.py` - MultiHeadSelfAttention implementation
  - `embeddings.py` - TokenEmbedding, PositionalEncoding
  - `layers.py` - FeedForwardBlock and other neural network layers
- `preprocessing/` - Data pipeline
  - `tokenizer.py` - Custom BPE tokenizer implementation
  - `data_loaders.py` - TextDataset, collate_batch functions
- `training/` - Training infrastructure
  - `trainer.py` - Main Trainer class with training loop
  - `optimizers.py` - Optimizer configurations
  - `schedulers.py` - Learning rate schedulers
- `evaluation/` - Model evaluation
  - `metrics.py` - Accuracy and other metrics
  - `perplexity.py` - Perplexity calculation
- `utils/` - Utilities
  - `checkpoint_manager.py` - Model checkpointing
  - `logging.py` - SimpleLogger implementation

### `/data/` - Data Storage
**Organized by processing stage and phase:**

- `raw/` - Original datasets
  - `english_corpus/` - Raw English text files
  - `code_corpus/` - Raw code datasets (Phase 2)
  - `multimodal_data/` - Images and multimodal data (Phase 3)
- `processed/` - Tokenized and processed data
  - `english_tokens/` - Tokenized English corpus
  - `code_tokens/` - Tokenized code data
  - `multimodal_features/` - Processed multimodal features
- `vocab/` - Tokenizer artifacts
  - `tokenizer_model.json` - BPE vocabulary
  - `merges.json` - BPE merge rules

### `/checkpoints/` - Model Checkpoints
**Organized by training phase:**

- `phase1_english/` - English comprehension model checkpoints
- `phase2_code/` - Code generation model checkpoints  
- `phase3_multimodal/` - Multimodal model checkpoints

### `/scripts/` - Executable Scripts
**Entry points for common tasks:**

- `preprocess_data.py` - Data download and preprocessing
- Training scripts for each phase (planned)

### `/notebooks/` - Jupyter Notebooks
**For experimentation and Colab training:**

- Colab training notebooks for each phase
- Local development and testing notebooks

### `/logs/` - Training Logs
**Timestamped training logs for monitoring progress**

## Code Conventions

### Import Structure
- Absolute imports from `src/` root
- Clear module dependencies following the directory structure

### Configuration Pattern
- All hyperparameters centralized in config classes
- Separate configs for model, training, and data
- Config classes include `__repr__` for debugging

### Device Handling
- Automatic device detection (MPS/CUDA/CPU)
- Device-agnostic code with appropriate optimizations
- Mixed precision training support

### Error Handling
- Graceful handling of missing files and checkpoints
- Clear error messages for setup issues
- Validation of data and model configurations

### Naming Conventions
- Snake_case for files and variables
- PascalCase for classes
- Descriptive names reflecting functionality
- Consistent checkpoint naming: `model_epoch_X.pt`