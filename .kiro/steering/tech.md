# Technical Stack

## Core Technologies
- **Language**: Python
- **Deep Learning Framework**: PyTorch (preferred for flexibility and Pythonic nature)
- **Hardware Acceleration**: 
  - Apple Metal Performance Shaders (MPS) for MacBook M3 local development
  - NVIDIA GPUs via Google Colab for intensive training
- **Data Processing**: Custom tokenization (BPE) and data loading pipelines
- **Version Control**: Git

## Key Libraries & Dependencies
- `torch` - PyTorch with MPS support for M3 optimization
- `datasets` - Hugging Face datasets for corpus acquisition
- `collections` - For BPE tokenizer implementation
- `json` - Data serialization and tokenizer persistence
- Standard library: `os`, `time`, `logging`

## Development Environment
- **Local Development**: MacBook M3 with MPS backend
- **Training**: Google Colab Pro/Plus (V100/A100 GPUs)
- **Mixed Precision**: `torch.autocast` and `torch.cuda.amp.GradScaler` for memory optimization

## Common Commands

### Data Preprocessing
```bash
# Download and preprocess English corpus
python scripts/preprocess_data.py

# Train custom BPE tokenizer (integrated in preprocessing)
python -m src.preprocessing.tokenizer
```

### Training
```bash
# Train Phase 1 (English comprehension)
python -m src.training.trainer

# Alternative training scripts
python scripts/train_english.py
```

### Model Testing
```bash
# Test individual components
python -m src.model.transformer
python -m src.model.attention
python -m src.preprocessing.tokenizer
```

### Checkpoint Management
```bash
# Checkpoints are automatically saved to checkpoints/phase1_english/
# Load and resume training from specific checkpoint via trainer
```

## Architecture Patterns
- **Custom Implementation**: All core components built from scratch (attention, embeddings, layers)
- **Modular Design**: Clear separation between model, training, preprocessing, and utilities
- **Configuration-Driven**: Centralized config classes for model, training, and data parameters
- **Device Agnostic**: Automatic device detection (MPS/CUDA/CPU) with appropriate optimizations