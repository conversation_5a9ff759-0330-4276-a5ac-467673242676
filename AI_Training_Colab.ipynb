{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 🚀 SUPER-FAST GPU Training on Google Colab\n", "\n", "This notebook trains your improved AI model with GPU acceleration for FAST training!\n", "\n", "## Setup Instructions:\n", "1. **IMPORTANT:** Change runtime to GPU (Runtime → Change runtime type → GPU)\n", "2. Upload the `ai_training_colab.zip` file to this Colab session\n", "3. Run all cells in order\n", "4. Download the trained model when complete\n", "\n", "## Expected Training Time: 30-60 minutes! 🚀\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 📦 Extract uploaded files\n", "import zipfile\n", "import os\n", "\n", "# Extract the training package\n", "with zipfile.ZipFile('ai_training_colab.zip', 'r') as zip_ref:\n", "    zip_ref.extractall('.')\n", "\n", "print('✅ Files extracted successfully!')\n", "\n", "# List contents\n", "for root, dirs, files in os.walk('.'):\n", "    level = root.replace('.', '').count(os.sep)\n", "    indent = ' ' * 2 * level\n", "    print(f'{indent}{os.path.basename(root)}/')\n", "    subindent = ' ' * 2 * (level + 1)\n", "    for file in files[:5]:  # Show first 5 files\n", "        print(f'{subindent}{file}')\n", "    if len(files) > 5:\n", "        print(f'{subindent}... and {len(files)-5} more files')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 🔥 Install requirements\n", "!pip install -r requirements.txt\n", "\n", "# Check GPU\n", "import torch\n", "print(f'PyTorch version: {torch.__version__}')\n", "print(f'CUDA available: {torch.cuda.is_available()}')\n", "if torch.cuda.is_available():\n", "    print(f'🔥 GPU: {torch.cuda.get_device_name()}')\n", "    print(f'🔥 GPU Memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB')\n", "else:\n", "    print('❌ GPU not detected! Make sure runtime is set to GPU')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 🔥 Start SUPER-FAST GPU training!\n", "!python colab_train.py"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 📦 Package trained model for download\n", "import zipfile\n", "import os\n", "from pathlib import Path\n", "\n", "# Create download package\n", "with zipfile.ZipFile('trained_model.zip', 'w') as zipf:\n", "    # Add checkpoints\n", "    if os.path.exists('checkpoints'):\n", "        for root, dirs, files in os.walk('checkpoints'):\n", "            for file in files:\n", "                file_path = os.path.join(root, file)\n", "                zipf.write(file_path, file_path)\n", "    \n", "    # Add tokenizer\n", "    if os.path.exists('data/vocab'):\n", "        for root, dirs, files in os.walk('data/vocab'):\n", "            for file in files:\n", "                file_path = os.path.join(root, file)\n", "                zipf.write(file_path, file_path)\n", "\n", "print('✅ Trained model packaged as trained_model.zip')\n", "print('📥 Download this file to get your trained model!')"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"name": "python", "version": "3.8.0"}}, "nbformat": 4, "nbformat_minor": 4}