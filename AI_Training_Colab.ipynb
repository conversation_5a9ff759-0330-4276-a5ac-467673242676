# 📦 Extract uploaded files
import zipfile
import os

# Extract the training package
with zipfile.ZipFile('ai_training_colab.zip', 'r') as zip_ref:
    zip_ref.extractall('.')

print('✅ Files extracted successfully!')

# List contents
for root, dirs, files in os.walk('.'):
    level = root.replace('.', '').count(os.sep)
    indent = ' ' * 2 * level
    print(f'{indent}{os.path.basename(root)}/')
    subindent = ' ' * 2 * (level + 1)
    for file in files[:5]:  # Show first 5 files
        print(f'{subindent}{file}')
    if len(files) > 5:
        print(f'{subindent}... and {len(files)-5} more files')

# 🔧 Install requirements
!pip install -r requirements.txt

# Check GPU
import torch
print(f'CUDA available: {torch.cuda.is_available()}')
if torch.cuda.is_available():
    print(f'GPU: {torch.cuda.get_device_name()}')
    print(f'Memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB')

# 🚀 Start training!
!python colab_train.py

# 📦 Package trained model for download
import zipfile
import os
from pathlib import Path

# Create download package
with zipfile.ZipFile('trained_model.zip', 'w') as zipf:
    # Add checkpoints
    if os.path.exists('checkpoints'):
        for root, dirs, files in os.walk('checkpoints'):
            for file in files:
                file_path = os.path.join(root, file)
                zipf.write(file_path, file_path)
    
    # Add tokenizer
    if os.path.exists('data/vocab'):
        for root, dirs, files in os.walk('data/vocab'):
            for file in files:
                file_path = os.path.join(root, file)
                zipf.write(file_path, file_path)

print('✅ Trained model packaged as trained_model.zip')
print('📥 Download this file to get your trained model!')