# Data Expansion Plan for Enhanced English Language Model Training

## Overview
This document outlines a comprehensive plan for expanding the training dataset to improve the English language model's performance. The plan focuses on acquiring diverse, high-quality English text data while maintaining manageable dataset sizes for local training.

## Current Status
- **Current Dataset**: Small English corpus with ~1000 vocabulary size
- **Current Performance**: Basic text generation with limited coherence
- **Hardware**: MacBook M3 (optimized for local training)
- **Target**: Significantly improved English text generation capabilities

## Phase 1: Immediate Dataset Expansion (1-10GB)

### 1.1 OpenWebText Subset
**Source**: Hugging Face `Skylion007/openwebtext`
**Size**: ~40GB full dataset, target 1-2GB subset
**Quality**: High-quality web text used to train GPT-2

**Implementation Steps**:
```python
# Download and process OpenWebText subset
from datasets import load_dataset
import random

# Load dataset with streaming to manage memory
dataset = load_dataset("Skylion007/openwebtext", streaming=True)

# Sample a manageable subset (e.g., 100K documents)
subset_size = 100000
sampled_data = []
for i, example in enumerate(dataset['train']):
    if i >= subset_size:
        break
    if len(example['text']) > 100:  # Filter very short texts
        sampled_data.append(example['text'])
```

### 1.2 Wikipedia English Subset
**Source**: Hugging Face `wikipedia` dataset
**Size**: ~20GB full, target 500MB-1GB subset
**Quality**: High-quality encyclopedic content

**Implementation Steps**:
```python
# Load English Wikipedia
wiki_dataset = load_dataset("wikipedia", "20220301.en", streaming=True)

# Filter and sample articles
filtered_articles = []
for article in wiki_dataset['train']:
    text = article['text']
    if len(text) > 500 and len(text) < 10000:  # Medium-length articles
        filtered_articles.append(text)
    if len(filtered_articles) >= 50000:  # Target 50K articles
        break
```

### 1.3 BookCorpus Subset
**Source**: Available through various academic sources
**Size**: Target 500MB-1GB
**Quality**: Literary text for diverse language patterns

## Phase 2: Medium-Scale Expansion (10-50GB)

### 2.1 C4 (Colossal Clean Crawled Corpus) Subset
**Source**: Hugging Face `c4` dataset
**Size**: ~750GB full, target 5-10GB subset
**Quality**: Cleaned Common Crawl data

**Implementation Strategy**:
- Use the `realnewslike` subset of C4 for higher quality
- Filter by language confidence scores
- Remove duplicates and low-quality content

### 2.2 RedPajama Subset
**Source**: Together AI's RedPajama dataset
**Size**: Target 5-10GB subset
**Quality**: Diverse web content similar to LLaMA training data

## Phase 3: Advanced Dataset Integration (50GB+)

### 3.1 Custom Filtering Pipeline
**Objective**: Create a robust pipeline for processing large datasets

**Components**:
1. **Language Detection**: Ensure English-only content
2. **Quality Filtering**: Remove low-quality, repetitive, or spam content
3. **Deduplication**: Remove near-duplicate content
4. **Length Filtering**: Optimal sequence lengths for training
5. **Content Filtering**: Remove inappropriate or harmful content

### 3.2 Domain-Specific Datasets
- **News**: Reuters, CNN/DailyMail datasets
- **Academic**: ArXiv papers (English abstracts and conclusions)
- **Conversational**: OpenSubtitles, PersonaChat
- **Code Comments**: GitHub code with English comments

## Implementation Timeline

### Week 1-2: Infrastructure Setup
- [ ] Create data download and processing scripts
- [ ] Set up data storage and management system
- [ ] Implement quality filtering pipeline
- [ ] Test with small samples

### Week 3-4: Phase 1 Implementation
- [ ] Download and process OpenWebText subset
- [ ] Download and process Wikipedia subset
- [ ] Integrate with existing preprocessing pipeline
- [ ] Test training with expanded dataset

### Week 5-8: Phase 2 Implementation
- [ ] Implement C4 subset processing
- [ ] Add RedPajama subset
- [ ] Optimize data loading and preprocessing
- [ ] Conduct training experiments with larger datasets

### Week 9-12: Phase 3 Implementation
- [ ] Implement advanced filtering pipeline
- [ ] Add domain-specific datasets
- [ ] Optimize for M3 hardware performance
- [ ] Conduct comprehensive evaluation

## Technical Implementation

### Data Processing Pipeline
```python
class DataExpansionPipeline:
    def __init__(self, output_dir: str, target_size_gb: float):
        self.output_dir = output_dir
        self.target_size_gb = target_size_gb
        self.current_size = 0
        
    def download_openwebtext_subset(self, sample_ratio: float = 0.01):
        """Download and process OpenWebText subset"""
        pass
        
    def download_wikipedia_subset(self, max_articles: int = 100000):
        """Download and process Wikipedia subset"""
        pass
        
    def apply_quality_filters(self, text: str) -> bool:
        """Apply quality filtering to text"""
        # Length filtering
        if len(text) < 100 or len(text) > 50000:
            return False
            
        # Language detection
        # Quality scoring
        # Content filtering
        return True
        
    def deduplicate_content(self, texts: List[str]) -> List[str]:
        """Remove duplicate and near-duplicate content"""
        pass
```

### Integration with Existing System
1. **Tokenizer Updates**: Retrain tokenizer on expanded dataset for better vocabulary
2. **Model Scaling**: Adjust model capacity based on dataset size
3. **Training Schedule**: Longer training with learning rate scheduling
4. **Evaluation**: Comprehensive evaluation on diverse test sets

## Expected Outcomes

### Phase 1 Results (1-10GB)
- **Vocabulary**: Expand from 1K to 8K-16K tokens
- **Model Performance**: Significant improvement in coherence and fluency
- **Training Time**: 2-5x longer training time
- **Quality**: Better handling of diverse topics and writing styles

### Phase 2 Results (10-50GB)
- **Vocabulary**: Expand to 32K-50K tokens
- **Model Performance**: Near-commercial quality for specific domains
- **Capabilities**: Better understanding of context and nuanced language
- **Generalization**: Improved performance across different text types

### Phase 3 Results (50GB+)
- **Vocabulary**: 50K+ tokens with subword efficiency
- **Model Performance**: High-quality text generation
- **Robustness**: Consistent performance across domains
- **Efficiency**: Optimized for M3 hardware capabilities

## Resource Requirements

### Storage
- **Phase 1**: 10-20GB storage
- **Phase 2**: 50-100GB storage  
- **Phase 3**: 100-200GB storage

### Compute
- **Download/Processing**: 10-20 hours per phase
- **Training**: 2-10x current training time per phase
- **Memory**: 16-32GB RAM recommended for large dataset processing

### Network
- **Bandwidth**: High-speed internet for dataset downloads
- **Time**: Several hours to days for large dataset downloads

## Risk Mitigation

### Technical Risks
- **Memory Issues**: Implement streaming and chunked processing
- **Storage Limitations**: Implement data compression and cleanup
- **Training Instability**: Gradual dataset expansion with validation

### Quality Risks
- **Data Quality**: Implement robust filtering and validation
- **Bias and Fairness**: Monitor for dataset biases and harmful content
- **Legal Compliance**: Ensure proper licensing and usage rights

## Success Metrics

### Quantitative Metrics
- **Perplexity**: Target <50 on validation set
- **BLEU Score**: Improvement on text generation tasks
- **Vocabulary Coverage**: 95%+ coverage of common English words
- **Training Stability**: Consistent loss reduction over epochs

### Qualitative Metrics
- **Coherence**: Generated text maintains topic and context
- **Fluency**: Natural-sounding language generation
- **Diversity**: Ability to generate varied content styles
- **Factual Accuracy**: Improved factual consistency (where applicable)

## Conclusion

This data expansion plan provides a structured approach to significantly improving the English language model through careful dataset curation and expansion. The phased approach allows for incremental improvements while managing resource constraints and technical challenges.

The plan prioritizes high-quality, diverse English text sources and includes robust processing pipelines to ensure optimal training data quality. Success will be measured through both quantitative metrics and qualitative assessment of generated text quality.
