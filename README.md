# AI Language Model from Scratch

This project is an ambitious endeavor to build a comprehensive AI language model from the ground up, utilizing a transformer-based architecture. The development emphasizes a phased training approach, starting with foundational English language understanding, progressing to code generation, and culminating in advanced reasoning and multi-modal capabilities. A core aspect of this project is to minimize external API dependencies for the model's core components, focusing on custom implementations.

## ✨ Goals

-   **Custom Transformer Implementation**: Develop a robust transformer-based language model entirely from scratch.
-   **Staged Training Progression**: Implement a clear and incremental training roadmap across three distinct phases.
-   **Optimized Local Development**: Ensure an efficient development workflow on MacBook M3 hardware, leveraging Apple's Metal Performance Shaders (MPS).
-   **Scalable Training**: Integrate seamlessly with Google Colab for computationally intensive training phases with access to powerful GPUs.
-   **Modular Design**: Maintain a clean, modular, and easily extensible codebase for future enhancements and research.

## 🚀 Features & Capabilities

### Phase 1: English Language Comprehension
-   **Objective**: Master foundational English language understanding and generation.
-   **Key Tasks**: Masked Language Modeling (MLM), Next Sentence Prediction (NSP), Causal Language Modeling (CLM).
-   **Evaluation**: Perplexity, Accuracy, qualitative analysis.

### Phase 2: Code Understanding & Generation
-   **Objective**: Extend the model to comprehend and generate programming code.
-   **Key Tasks**: Code Completion, Fill-in-the-Middle, Docstring Generation, Code Summarization.
-   **Evaluation**: Exact Match/Pass@k, BLEU/ROUGE (with caveats), custom syntax checks.

### Phase 3: Advanced Reasoning & Multi-modal Capabilities
-   **Objective**: Enable complex reasoning and integrate multiple data modalities (e.g., text and images).
-   **Key Tasks**: Visual Question Answering (VQA), Image Captioning, complex logical deductions.
-   **Evaluation**: VQA Accuracy, CIDEr, SPICE, METEOR, human evaluation.

## 🛠️ Technical Stack

-   **Core Language**: Python
-   **Deep Learning Framework**: PyTorch (with a preference for its flexibility and Pythonic nature)
-   **Hardware Acceleration**: Apple Metal Performance Shaders (MPS) for MacBook M3, NVIDIA GPUs via Google Colab
-   **Data Processing**: Custom tokenization (BPE/WordPiece) and data loading pipelines.
-   **Version Control**: Git

## 📁 Project Structure

The project is organized into the following key directories:

```
./
├── data/             # Raw and processed datasets, tokenizer vocabularies
├── src/              # All source code (config, model, preprocessing, training, evaluation, utils)
├── scripts/          # Executable scripts for training, preprocessing, etc.
├── notebooks/        # Jupyter notebooks for Colab training and local experimentation
├── checkpoints/      # Stored model weights and training states
├── .gitignore        # Git ignore file
├── README.md         # Project overview
└── Plan.md           # Comprehensive project plan
```

## 🏁 Getting Started

Detailed setup instructions, training guides, and development workflows are provided in `Plan.md`. Generally, you will:

1.  **Set up your environment**: Install Python, PyTorch (with MPS support), and other dependencies.
2.  **Prepare Data**: Acquire raw datasets and run preprocessing scripts to generate tokenized data.
3.  **Train Models**: Utilize the provided scripts and notebooks to train the model in phases, leveraging Google Colab for intensive tasks and your MacBook M3 for local iterations.
4.  **Evaluate & Iterate**: Regularly evaluate model performance and refine the architecture and training strategy. 